# Documentation

This folder contains comprehensive documentation for the Togeda.ai application architecture and implementation.

## Subscription System

### [SUBSCRIPTION_ENTRY_IMPLEMENTATION.md](./SUBSCRIPTION_ENTRY_IMPLEMENTATION.md)
**Current Implementation Guide** - Complete documentation of the flat subscription architecture currently in use.

**Contents:**
- Architecture overview and collection structure
- Core services and methods (`FlatSubscriptionService`)
- Subscription sources (Stripe, perks, giveaways, free)
- Hooks and state management
- Common patterns and best practices
- API endpoints and security
- Performance optimizations

**Use this for:** Understanding how the subscription system works, implementing new features, troubleshooting.

### [SUBSCRIPTION_MIGRATION_GUIDE.md](./SUBSCRIPTION_MIGRATION_GUIDE.md)
**Migration History** - Documentation of the completed migration from old to flat subscription architecture.

**Contents:**
- Migration status (completed)
- Final database state
- Migration scripts and commands
- Rollback procedures (if needed)
- Post-migration verification steps

**Use this for:** Understanding the migration history, reference for future migrations, rollback procedures.

## Quick Reference

### Current Subscription Architecture
- **Collection**: `userSubscriptions/{subscriptionId}` (flat structure)
- **Service**: `FlatSubscriptionService`
- **Store**: `useUserSubscriptionStore`
- **Hooks**: `flat-subscription.realtime.hooks.ts`

### Key Statistics (Post-Migration)
- ✅ **24 users** successfully migrated
- ✅ **30 subscription entries** total
- ✅ **4 Stripe subscriptions** (paid users)
- ✅ **1 perk subscription** (referral/promotional)
- ✅ **25 free subscriptions** (fallback for all users)
- ✅ **0 old structure entries** (migration complete)

### Precedence System
1. **Perk** (precedence: 1) - Highest priority
2. **Giveaway** (precedence: 2) - Medium priority
3. **Stripe** (precedence: 3) - Low priority
4. **Free** (precedence: 999) - Lowest priority (fallback)

### Common Commands
```bash
# Validate current subscription state
npm run migrate:flat-subscriptions:simple -- --validate-only

# Validate perk migration state
npm run migrate:subscription-perks -- --validate-only

# Emergency rollback (if needed)
npm run migrate:flat-subscriptions:simple -- --rollback
```

## Contributing

When adding new subscription-related features:

1. **Always use `FlatSubscriptionService`** - Never query subscription collections directly
2. **Follow precedence rules** - Ensure new subscription sources respect the precedence system
3. **Update documentation** - Keep this documentation current with any changes
4. **Test with multiple subscription types** - Verify behavior with Stripe, perk, and free subscriptions
5. **Consider multi-user scenarios** - Test squad member badge functionality

## Support

For questions about the subscription system:
1. Check `SUBSCRIPTION_ENTRY_IMPLEMENTATION.md` for current implementation details
2. Review `SUBSCRIPTION_MIGRATION_GUIDE.md` for migration history
3. Run validation commands to check system health
4. Check Firestore Console for subscription entry data

---

**Last Updated:** December 2024  
**Migration Status:** ✅ Complete  
**Architecture:** Flat Subscription Entries
