# Subscription Migration Guide

## ✅ Migration Status: COMPLETED

### ✅ Code Migration: COMPLETED

- All components, services, and hooks have been updated to use `FlatSubscriptionService`
- The application is ready for the new flat subscription architecture
- No active code is using the old `UserSubscriptionService`

### ✅ Database Migration: COMPLETED

- All 24 users successfully migrated to flat `userSubscriptions/{subscriptionId}` structure
- All subscription perks migrated to flat subscription entries
- 0 old structure entries remain - migration 100% complete

## Final Database State

Migration completed successfully:

- **Total users**: 24
- **Total subscription entries**: 30
- **Free entries**: 25 (all users have free fallback)
- **Stripe entries**: 4 (paid subscriptions)
- **Perk entries**: 1 (subscription perks)
- **Old structure entries**: 0 (fully migrated)
- **Current structure**: `userSubscriptions/{subscriptionId}` (flat collection) ✅

## Migration Scripts Available

### 1. Simple Migration Script (Recommended)

**File**: `scripts/migrate-to-flat-subscriptions-simple.ts`

**Commands**:

```bash
# Validate current state
npm run migrate:flat-subscriptions:simple -- --validate-only

# Backup existing data (RECOMMENDED FIRST STEP)
npm run migrate:flat-subscriptions:simple -- --backup-only

# Dry run (preview changes)
npm run migrate:flat-subscriptions:simple -- --dry-run

# Execute full migration
npm run migrate:flat-subscriptions:simple

# Emergency rollback (if needed)
npm run migrate:flat-subscriptions:simple -- --rollback
```

**Environment-specific commands**:

```bash
# Production
npm run migrate:flat-subscriptions:simple:prod -- --validate-only
npm run migrate:flat-subscriptions:simple:prod -- --backup-only
npm run migrate:flat-subscriptions:simple:prod

# Staging
npm run migrate:flat-subscriptions:simple:staging -- --validate-only
npm run migrate:flat-subscriptions:simple:staging -- --backup-only
npm run migrate:flat-subscriptions:simple:staging
```

### 2. Advanced Migration Script (Requires commander package)

**File**: `scripts/migrate-to-flat-subscriptions.ts`

This script has more advanced CLI options but requires the `commander` package to be installed.

## Migration Process

### Step 1: Backup (CRITICAL)

```bash
npm run migrate:flat-subscriptions:simple -- --backup-only
```

This creates a backup in the `userSubscriptionsBackup` collection.

### Step 2: Validate Current State

```bash
npm run migrate:flat-subscriptions:simple -- --validate-only
```

This shows current statistics and identifies issues.

### Step 3: Dry Run

```bash
npm run migrate:flat-subscriptions:simple -- --dry-run
```

This previews what the migration would do without making changes.

### Step 4: Execute Migration

```bash
npm run migrate:flat-subscriptions:simple
```

This performs the actual migration:

1. Backs up existing data
2. Converts old subscription documents to flat entries
3. Creates free subscription entries for users without subscriptions
4. Deletes original subscription documents
5. Validates migration results

### Step 5: Validate Results

The migration automatically validates results, but you can run validation separately:

```bash
npm run migrate:flat-subscriptions:simple -- --validate-only
```

## What the Migration Does

### For Users with Active Subscriptions

- Creates a Stripe subscription entry with their current subscription data
- Creates a free subscription entry as fallback
- Deletes the old subscription document

### For Users with Free/No Subscriptions

- Creates a free subscription entry
- Deletes the old subscription document (if exists)

### For Users Without Any Subscription Data

- Creates a free subscription entry

## New Flat Architecture Benefits

### 1. Multiple Subscription Sources

- **Stripe**: Paid subscriptions
- **Perk**: Referral-based perks
- **Giveaway**: Promotional subscriptions
- **Free**: Default subscription

### 2. Precedence System

- **Perk**: Highest priority (1)
- **Giveaway**: Medium priority (2)
- **Stripe**: Low priority (3)
- **Free**: Lowest priority (999)

### 3. Better Performance

- Efficient multi-user queries for squad member badges
- Simplified data access patterns
- Reduced Firestore queries

### 4. Enhanced Features

- Pause/resume functionality
- Duration tracking
- Multiple concurrent subscriptions

## Emergency Rollback

If something goes wrong, you can rollback:

```bash
npm run migrate:flat-subscriptions:simple -- --rollback
```

This will:

1. Delete all new flat subscription entries
2. Restore original subscription documents from backup
3. Return to the old structure

## Environment Variables Required

Ensure these are set in your `.env.local` (or production/staging env files):

```
FIREBASE_SERVICE_ACCOUNT_KEY={"type":"service_account",...}
NEXT_PUBLIC_FIREBASE_PROJECT_ID=your-project-id
```

## Post-Migration Verification

After migration, verify:

1. All users can access the application
2. Subscription status displays correctly
3. Pro features work for subscribed users
4. Squad member badges show correctly
5. No console errors related to subscriptions

## Troubleshooting

### Common Issues

1. **Firebase Authentication Error**

   - Verify `FIREBASE_SERVICE_ACCOUNT_KEY` is correctly set
   - Ensure service account has Firestore read/write permissions

2. **Validation Failures**

   - Check that all users have at least one subscription entry
   - Verify no users have multiple applied entries

3. **Migration Errors**
   - Check the error logs for specific user issues
   - Consider migrating problematic users individually

### Individual User Migration

If needed, you can modify the script to migrate specific users by adding user ID filtering to the `AdminMigrationService.migrateAllUsers()` method.

## Next Steps After Migration

1. **Monitor Application**: Watch for any subscription-related issues
2. **Test Features**: Verify all subscription-dependent features work
3. **Clean Up**: After confirming everything works, you can remove backup data
4. **Update Documentation**: Update any internal docs referencing the old structure

## Files Modified/Created

### New Files

- `lib/domains/user-subscription/admin-migration.service.ts`
- `scripts/migrate-to-flat-subscriptions-simple.ts`
- `SUBSCRIPTION_MIGRATION_GUIDE.md`

### Modified Files

- `package.json` (added migration scripts)
- `scripts/migrate-to-flat-subscriptions.ts` (updated to use Admin SDK)

### Existing Architecture

- `lib/domains/user-subscription/flat-subscription.service.ts` (already in use)
- `lib/domains/user-subscription/user-subscription.store.ts` (already using flat service)
- All components and hooks (already migrated to flat architecture)
