import Stripe from "stripe"
import { FlatSubscriptionService } from "./flat-subscription.service"
import { StripeSubscriptionData, UserSubscriptionEntry } from "./user-subscription.types"

/**
 * Webhook handlers for flat subscription system
 */
export class FlatSubscriptionWebhooks {

  /**
   * Handle Stripe subscription created webhook
   */
  static async handleSubscriptionCreated(
    subscription: Stripe.Subscription,
    customer: Stripe.Customer
  ): Promise<{ success: boolean; error?: Error }> {
    try {
      console.log(`🎉 Processing subscription created for customer: ${customer.id}`)

      // Extract user ID from customer metadata
      const userId = customer.metadata?.userId
      if (!userId) {
        throw new Error("Customer missing userId in metadata")
      }

      // Create Stripe subscription data
      const stripeData: StripeSubscriptionData = {
        customerId: customer.id,
        subscriptionId: subscription.id,
        subscriptionStatus: subscription.status as any,
        subscriptionPlan: this.extractPlanFromSubscription(subscription),
        currentPeriodEnd: new Date(subscription.current_period_end * 1000) as any
      }

      // Add Stripe subscription entry
      const result = await FlatSubscriptionService.addStripeSubscription(userId, stripeData)

      if (!result.success) {
        throw new Error(`Failed to create subscription entry: ${result.error?.message}`)
      }

      console.log(`✅ Stripe subscription entry created for user ${userId}`)
      return { success: true }

    } catch (error) {
      console.error("❌ Error handling subscription created:", error)
      return { success: false, error: error as Error }
    }
  }

  /**
   * Handle Stripe subscription updated webhook
   */
  static async handleSubscriptionUpdated(
    subscription: Stripe.Subscription,
    customer: Stripe.Customer
  ): Promise<{ success: boolean; error?: Error }> {
    try {
      console.log(`🔄 Processing subscription updated for customer: ${customer.id}`)

      const userId = customer.metadata?.userId
      if (!userId) {
        throw new Error("Customer missing userId in metadata")
      }

      // Find existing Stripe subscription entry
      const allSubscriptions = await FlatSubscriptionService.getUserSubscriptions(userId)
      const stripeEntry = allSubscriptions.find(
        sub => sub.source === "stripe" && 
               (sub.subscriptionData as StripeSubscriptionData).subscriptionId === subscription.id
      )

      if (!stripeEntry) {
        console.log(`⚠️  No existing Stripe entry found for subscription ${subscription.id}, creating new one`)
        return await this.handleSubscriptionCreated(subscription, customer)
      }

      // Update subscription data
      const updatedStripeData: StripeSubscriptionData = {
        customerId: customer.id,
        subscriptionId: subscription.id,
        subscriptionStatus: subscription.status as any,
        subscriptionPlan: this.extractPlanFromSubscription(subscription),
        currentPeriodEnd: new Date(subscription.current_period_end * 1000) as any
      }

      // Determine new status based on Stripe status
      let newStatus = stripeEntry.status
      if (subscription.status === "active" || subscription.status === "trialing") {
        // If subscription is active, check if it should be applied or paused
        const currentSubscription = await FlatSubscriptionService.getCurrentSubscription(userId)
        
        // If no current subscription or current has lower precedence, apply this one
        if (!currentSubscription || currentSubscription.precedence > stripeEntry.precedence) {
          newStatus = "applied"
          
          // Pause the current subscription if it exists and has lower precedence
          if (currentSubscription && currentSubscription.precedence > stripeEntry.precedence) {
            await FlatSubscriptionService.updateSubscriptionEntry(currentSubscription.id, {
              status: "paused",
              pausedAt: new Date() as any,
              activeDays: this.calculateActiveDays(currentSubscription)
            })
          }
        } else {
          newStatus = "pending"
        }
      } else if (subscription.status === "canceled" || subscription.status === "incomplete_expired") {
        newStatus = "expired"
      }

      // Update the subscription entry
      const updateResult = await FlatSubscriptionService.updateSubscriptionEntry(stripeEntry.id, {
        subscriptionData: updatedStripeData,
        status: newStatus,
        endDate: subscription.status === "canceled" ? new Date() as any : undefined
      })

      if (!updateResult.success) {
        throw new Error(`Failed to update subscription entry: ${updateResult.error?.message}`)
      }

      // If this subscription was canceled/expired, check if we need to reactivate another subscription
      if (newStatus === "expired" && stripeEntry.status === "applied") {
        await FlatSubscriptionService.processExpiredSubscriptions(userId)
      }

      console.log(`✅ Stripe subscription updated for user ${userId}, status: ${newStatus}`)
      return { success: true }

    } catch (error) {
      console.error("❌ Error handling subscription updated:", error)
      return { success: false, error: error as Error }
    }
  }

  /**
   * Handle Stripe subscription deleted webhook
   */
  static async handleSubscriptionDeleted(
    subscription: Stripe.Subscription,
    customer: Stripe.Customer
  ): Promise<{ success: boolean; error?: Error }> {
    try {
      console.log(`🗑️  Processing subscription deleted for customer: ${customer.id}`)

      const userId = customer.metadata?.userId
      if (!userId) {
        throw new Error("Customer missing userId in metadata")
      }

      // Find and delete the Stripe subscription entry
      const allSubscriptions = await FlatSubscriptionService.getUserSubscriptions(userId)
      const stripeEntry = allSubscriptions.find(
        sub => sub.source === "stripe" && 
               (sub.subscriptionData as StripeSubscriptionData).subscriptionId === subscription.id
      )

      if (!stripeEntry) {
        console.log(`⚠️  No Stripe entry found for deleted subscription ${subscription.id}`)
        return { success: true } // Not an error, subscription might have been manually deleted
      }

      // Delete the subscription entry
      const deleteResult = await FlatSubscriptionService.deleteSubscriptionEntry(stripeEntry.id)

      if (!deleteResult.success) {
        throw new Error(`Failed to delete subscription entry: ${deleteResult.error?.message}`)
      }

      // If this was the applied subscription, process expirations to activate next subscription
      if (stripeEntry.status === "applied") {
        await FlatSubscriptionService.processExpiredSubscriptions(userId)
      }

      console.log(`✅ Stripe subscription entry deleted for user ${userId}`)
      return { success: true }

    } catch (error) {
      console.error("❌ Error handling subscription deleted:", error)
      return { success: false, error: error as Error }
    }
  }

  /**
   * Handle Stripe invoice payment succeeded webhook
   */
  static async handleInvoicePaymentSucceeded(
    invoice: Stripe.Invoice,
    customer: Stripe.Customer
  ): Promise<{ success: boolean; error?: Error }> {
    try {
      console.log(`💰 Processing invoice payment succeeded for customer: ${customer.id}`)

      const userId = customer.metadata?.userId
      if (!userId) {
        throw new Error("Customer missing userId in metadata")
      }

      // If this is a subscription invoice, update the subscription
      if (invoice.subscription) {
        const subscriptionId = typeof invoice.subscription === "string" 
          ? invoice.subscription 
          : invoice.subscription.id

        // Find the subscription entry
        const allSubscriptions = await FlatSubscriptionService.getUserSubscriptions(userId)
        const stripeEntry = allSubscriptions.find(
          sub => sub.source === "stripe" && 
                 (sub.subscriptionData as StripeSubscriptionData).subscriptionId === subscriptionId
        )

        if (stripeEntry) {
          // Update the current period end if available
          const updatedStripeData = {
            ...(stripeEntry.subscriptionData as StripeSubscriptionData),
            currentPeriodEnd: invoice.period_end ? new Date(invoice.period_end * 1000) as any : undefined
          }

          await FlatSubscriptionService.updateSubscriptionEntry(stripeEntry.id, {
            subscriptionData: updatedStripeData
          })

          console.log(`✅ Updated subscription period end for user ${userId}`)
        }
      }

      return { success: true }

    } catch (error) {
      console.error("❌ Error handling invoice payment succeeded:", error)
      return { success: false, error: error as Error }
    }
  }

  /**
   * Handle Stripe invoice payment failed webhook
   */
  static async handleInvoicePaymentFailed(
    invoice: Stripe.Invoice,
    customer: Stripe.Customer
  ): Promise<{ success: boolean; error?: Error }> {
    try {
      console.log(`💸 Processing invoice payment failed for customer: ${customer.id}`)

      const userId = customer.metadata?.userId
      if (!userId) {
        throw new Error("Customer missing userId in metadata")
      }

      // If this is a subscription invoice, update the subscription status
      if (invoice.subscription) {
        const subscriptionId = typeof invoice.subscription === "string" 
          ? invoice.subscription 
          : invoice.subscription.id

        // Find the subscription entry
        const allSubscriptions = await FlatSubscriptionService.getUserSubscriptions(userId)
        const stripeEntry = allSubscriptions.find(
          sub => sub.source === "stripe" && 
                 (sub.subscriptionData as StripeSubscriptionData).subscriptionId === subscriptionId
        )

        if (stripeEntry) {
          // Update subscription status to past_due
          const updatedStripeData = {
            ...(stripeEntry.subscriptionData as StripeSubscriptionData),
            subscriptionStatus: "past_due" as any
          }

          await FlatSubscriptionService.updateSubscriptionEntry(stripeEntry.id, {
            subscriptionData: updatedStripeData
          })

          console.log(`⚠️  Updated subscription status to past_due for user ${userId}`)
        }
      }

      return { success: true }

    } catch (error) {
      console.error("❌ Error handling invoice payment failed:", error)
      return { success: false, error: error as Error }
    }
  }

  /**
   * Extract subscription plan from Stripe subscription
   */
  private static extractPlanFromSubscription(subscription: Stripe.Subscription): "monthly" | "yearly" {
    // Check the price/plan interval
    const priceInterval = subscription.items.data[0]?.price?.recurring?.interval
    
    if (priceInterval === "year") {
      return "yearly"
    } else {
      return "monthly" // Default to monthly
    }
  }

  /**
   * Calculate active days for a subscription
   */
  private static calculateActiveDays(subscription: UserSubscriptionEntry): number {
    if (!subscription.startDate) return 0
    
    const startDate = subscription.startDate instanceof Date 
      ? subscription.startDate 
      : subscription.startDate.toDate()
    
    const now = new Date()
    const diffTime = Math.abs(now.getTime() - startDate.getTime())
    return Math.ceil(diffTime / (1000 * 60 * 60 * 24))
  }
}
