"use client"

import { create } from "zustand"
import { persist, createJSONStorage } from "zustand/middleware"
import {
  UserSubscriptionEntry,
  SUBSCRIPTION_LIMITS,
  SubscriptionErrorType,
  StripeSubscriptionData,
} from "./user-subscription.types"
import { FlatSubscriptionService } from "./flat-subscription.service"
import { handleSubscriptionError } from "./user-subscription.errors"
import { AI_USAGE_LIMITS } from "@/lib/firebase/ai-usage-service"

/**
 * User subscription store state interface (simplified for flat collection)
 */
interface UserSubscriptionState {
  // Current subscription state
  currentSubscription: UserSubscriptionEntry | null
  allSubscriptions: UserSubscriptionEntry[]

  // Common state
  loading: boolean
  error: Error | null
  isSubscribed: boolean
  subscriptionPlan: "free" | "monthly" | "yearly" | null
  subscriptionStatus: "active" | "canceled" | "past_due" | "trialing" | "incomplete" | null
  lastAuthRefresh: number

  // Limits
  maxSquads: number
  maxTripsPerSquad: number
  maxDailyAIRequests: number
  maxWeeklyAIRequests: number
  hasTripChat: boolean

  // Subscription summary
  subscriptionSummary: {
    currentSource: string | null
    totalSubscriptions: number
    activeSubscriptions: number
    pausedSubscriptions: number
    expiredSubscriptions: number
  } | null

  // Actions
  setLoading: (loading: boolean) => void

  // Core flat subscription actions
  fetchCurrentSubscription: (userId: string) => Promise<void>
  fetchAllSubscriptions: (userId: string) => Promise<void>
  fetchSubscriptionSummary: (userId: string) => Promise<void>
  refreshSubscriptionIfNeeded: (userId: string) => void

  // Multi-user actions (for squad member badges)
  fetchMultiUserSubscriptions: (userIds: string[]) => Promise<UserSubscriptionEntry[]>

  // Utility actions
  canCreateMoreSquads: (userId: string, currentSquadCount?: number) => Promise<boolean>
  canCreateMoreTripsInSquad: (
    userId: string,
    squadId: string,
    currentTripCount?: number
  ) => Promise<boolean>
  hasFeatureAccess: (userId: string, feature: string) => Promise<boolean>

  // Error handling
  handleSubscriptionError: (errorType: SubscriptionErrorType) => void
  handleSubscriptionErrorWithRouter: (errorType: SubscriptionErrorType, router: any) => void
}

// Create a custom storage object that only uses localStorage on the client side
const customStorage = {
  getItem: (name: string) => {
    if (typeof window === "undefined") return null
    return window.localStorage.getItem(name)
  },
  setItem: (name: string, value: string) => {
    if (typeof window !== "undefined") {
      window.localStorage.setItem(name, value)
    }
  },
  removeItem: (name: string) => {
    if (typeof window !== "undefined") {
      window.localStorage.removeItem(name)
    }
  },
}

/**
 * User subscription store with Zustand
 */
export const useUserSubscriptionStore = create<UserSubscriptionState>()(
  persist(
    (set, get) => ({
      // Initial state
      currentSubscription: null,
      allSubscriptions: [],
      loading: true,
      error: null,
      isSubscribed: false,
      subscriptionPlan: "free",
      subscriptionStatus: null,
      lastAuthRefresh: 0,
      subscriptionSummary: null,

      // Default limits
      maxSquads: SUBSCRIPTION_LIMITS.FREE.MAX_SQUADS,
      maxTripsPerSquad: SUBSCRIPTION_LIMITS.FREE.MAX_TRIPS_PER_SQUAD,
      maxDailyAIRequests: AI_USAGE_LIMITS.FREE.DAILY,
      maxWeeklyAIRequests: AI_USAGE_LIMITS.FREE.WEEKLY,
      hasTripChat: SUBSCRIPTION_LIMITS.FREE.HAS_TRIP_CHAT,

      // Actions
      setLoading: (loading) => set({ loading }),

      // Fetch current subscription for a user
      fetchCurrentSubscription: async (userId: string) => {
        if (!userId) {
          set({
            currentSubscription: null,
            isSubscribed: false,
            subscriptionPlan: "free",
            subscriptionStatus: null,
            maxSquads: SUBSCRIPTION_LIMITS.FREE.MAX_SQUADS,
            maxTripsPerSquad: SUBSCRIPTION_LIMITS.FREE.MAX_TRIPS_PER_SQUAD,
            maxDailyAIRequests: AI_USAGE_LIMITS.FREE.DAILY,
            maxWeeklyAIRequests: AI_USAGE_LIMITS.FREE.WEEKLY,
            hasTripChat: SUBSCRIPTION_LIMITS.FREE.HAS_TRIP_CHAT,
            loading: false,
          })
          return
        }

        try {
          set({ loading: true, error: null })

          const currentSubscription = await FlatSubscriptionService.getCurrentSubscription(userId)
          const limits = await FlatSubscriptionService.getSubscriptionLimits(userId)

          const isSubscribed = currentSubscription?.source !== "free"
          // Determine subscription plan (only for Stripe subscriptions)
          const subscriptionPlan =
            currentSubscription?.source === "stripe"
              ? (currentSubscription.subscriptionData as StripeSubscriptionData).subscriptionPlan
              : "free"

          set({
            currentSubscription,
            isSubscribed,
            subscriptionPlan,
            subscriptionStatus:
              currentSubscription?.source === "stripe"
                ? (currentSubscription.subscriptionData as StripeSubscriptionData)
                    .subscriptionStatus
                : null,
            maxSquads: limits.maxSquads,
            maxTripsPerSquad: limits.maxTripsPerSquad,
            maxDailyAIRequests: limits.maxDailyAIRequests,
            maxWeeklyAIRequests: limits.maxWeeklyAIRequests,
            hasTripChat: limits.hasTripChat,
            loading: false,
          })
        } catch (error) {
          console.error("Error fetching current subscription:", error)
          set({
            error: error as Error,
            loading: false,
            // Set safe defaults on error
            currentSubscription: null,
            isSubscribed: false,
            subscriptionPlan: "free",
            maxSquads: SUBSCRIPTION_LIMITS.FREE.MAX_SQUADS,
            maxTripsPerSquad: SUBSCRIPTION_LIMITS.FREE.MAX_TRIPS_PER_SQUAD,
            maxDailyAIRequests: AI_USAGE_LIMITS.FREE.DAILY,
            maxWeeklyAIRequests: AI_USAGE_LIMITS.FREE.WEEKLY,
            hasTripChat: SUBSCRIPTION_LIMITS.FREE.HAS_TRIP_CHAT,
          })
        }
      },

      // Fetch all subscriptions for a user
      fetchAllSubscriptions: async (userId: string) => {
        if (!userId) {
          set({ allSubscriptions: [] })
          return
        }

        try {
          const allSubscriptions = await FlatSubscriptionService.getUserSubscriptions(userId)
          set({ allSubscriptions })
        } catch (error) {
          console.error("Error fetching all subscriptions:", error)
          set({ error: error as Error, allSubscriptions: [] })
        }
      },

      // Fetch subscription summary
      fetchSubscriptionSummary: async (userId: string) => {
        if (!userId) {
          set({ subscriptionSummary: null })
          return
        }

        try {
          const summary = await FlatSubscriptionService.getSubscriptionSummary(userId)
          set({
            subscriptionSummary: {
              currentSource: summary.currentSource,
              totalSubscriptions: summary.totalSubscriptions,
              activeSubscriptions: summary.activeSubscriptions,
              pausedSubscriptions: summary.pausedSubscriptions,
              expiredSubscriptions: summary.expiredSubscriptions,
            },
          })
        } catch (error) {
          console.error("Error fetching subscription summary:", error)
          set({ error: error as Error })
        }
      },

      // Multi-user subscriptions (for squad member badges)
      fetchMultiUserSubscriptions: async (userIds: string[]): Promise<UserSubscriptionEntry[]> => {
        try {
          return await FlatSubscriptionService.getMultiUserSubscriptions(userIds)
        } catch (error) {
          console.error("Error fetching multi-user subscriptions:", error)
          return []
        }
      },

      // Refresh subscription if needed (simplified)
      refreshSubscriptionIfNeeded: (userId: string) => {
        const { lastAuthRefresh } = get()
        const now = Date.now()
        const REFRESH_INTERVAL = 5 * 60 * 1000 // 5 minutes

        if (now - lastAuthRefresh > REFRESH_INTERVAL) {
          get().fetchCurrentSubscription(userId)
          set({ lastAuthRefresh: now })
        }
      },

      // Check if a user can create more squads
      canCreateMoreSquads: async (userId: string, currentSquadCount?: number) => {
        try {
          const limits = await FlatSubscriptionService.getSubscriptionLimits(userId)
          const currentCount = currentSquadCount ?? 0
          return currentCount < limits.maxSquads
        } catch (error) {
          console.error("Error checking if user can create more squads:", error)
          return false
        }
      },

      // Check if a user can create more trips in a squad
      canCreateMoreTripsInSquad: async (
        userId: string,
        _squadId: string, // squadId not used in flat structure but kept for API compatibility
        currentTripCount?: number
      ) => {
        try {
          const limits = await FlatSubscriptionService.getSubscriptionLimits(userId)
          const currentCount = currentTripCount ?? 0
          return currentCount < limits.maxTripsPerSquad
        } catch (error) {
          console.error("Error checking if user can create more trips in squad:", error)
          return false
        }
      },

      // Check if user has access to a specific feature
      hasFeatureAccess: async (userId: string, feature: string): Promise<boolean> => {
        try {
          return await FlatSubscriptionService.hasFeatureAccess(userId, feature)
        } catch (error) {
          console.error("Error checking feature access:", error)
          return false
        }
      },

      // Handle subscription errors
      handleSubscriptionError: (errorType: SubscriptionErrorType) => {
        const { isSubscribed } = get()
        handleSubscriptionError(errorType, isSubscribed)
      },

      // Handle subscription errors with router navigation
      handleSubscriptionErrorWithRouter: (errorType: SubscriptionErrorType, router: any) => {
        const { isSubscribed } = get()
        handleSubscriptionError(errorType, isSubscribed, router)
      },
    }),
    {
      name: "brotrips-user-subscription-storage",
      storage: createJSONStorage(() => customStorage),
      skipHydration: true, // Skip hydration to prevent hydration mismatch
      partialize: (state) => ({
        // Only persist these fields
        subscriptionPlan: state.subscriptionPlan,
        subscriptionStatus: state.subscriptionStatus,
      }),
    }
  )
)
