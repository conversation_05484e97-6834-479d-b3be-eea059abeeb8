"use client"

import { useEffect, useCallback } from "react"
import { useUserSubscriptionStore } from "./user-subscription.store"
import { useUser } from "../auth/auth.hooks"
import { useRouter, useSearchParams } from "next/navigation"
import { SubscriptionErrorType } from "./user-subscription.types"
import { toast } from "@/components/ui/use-toast"

// Export real-time hooks
export * from "./user-subscription.realtime.hooks"

/**
 * Selectors for user subscription store
 */

// Basic state selectors
export const useUserSubscription = () => useUserSubscriptionStore()
export const useUserSubscriptionLoading = () => useUserSubscriptionStore((state) => state.loading)
export const useIsUserSubscribed = () => useUserSubscriptionStore((state) => state.isSubscribed)
export const useUserSubscriptionPlan = () =>
  useUserSubscriptionStore((state) => state.subscriptionPlan)
export const useUserSubscriptionStatus = () =>
  useUserSubscriptionStore((state) => state.subscriptionStatus)
export const useUserSubscriptionDetails = () =>
  useUserSubscriptionStore((state) => state.currentSubscription)

// New hooks for subscription source information
export const useSubscriptionSource = () =>
  useUserSubscriptionStore((state) => state.currentSubscription?.source || "free")
export const useAllSubscriptions = () => useUserSubscriptionStore((state) => state.allSubscriptions)
export const useSubscriptionSummary = () =>
  useUserSubscriptionStore((state) => state.subscriptionSummary)

// Helper hooks for specific subscription types
export const useHasStripeSubscription = () =>
  useUserSubscriptionStore((state) => state.allSubscriptions.some((sub) => sub.source === "stripe"))
export const useHasActiveStripeSubscription = () =>
  useUserSubscriptionStore((state) => state.currentSubscription?.source === "stripe")
export const usePendingSubscriptions = () =>
  useUserSubscriptionStore((state) =>
    state.allSubscriptions.filter((sub) => sub.status === "pending" || sub.status === "paused")
  )

// Limit selectors
export const useMaxSquads = () => useUserSubscriptionStore((state) => state.maxSquads)
export const useMaxTripsPerSquad = () => useUserSubscriptionStore((state) => state.maxTripsPerSquad)
export const useMaxDailyAIRequests = () =>
  useUserSubscriptionStore((state) => state.maxDailyAIRequests)
export const useMaxWeeklyAIRequests = () =>
  useUserSubscriptionStore((state) => state.maxWeeklyAIRequests)
export const useHasTripChat = () => useUserSubscriptionStore((state) => state.hasTripChat)

// Action selectors
export const useFetchUserSubscription = () =>
  useUserSubscriptionStore((state) => state.fetchCurrentSubscription)
export const useFetchAllSubscriptions = () =>
  useUserSubscriptionStore((state) => state.fetchAllSubscriptions)
export const useFetchSubscriptionSummary = () =>
  useUserSubscriptionStore((state) => state.fetchSubscriptionSummary)
export const useCanCreateMoreSquads = () =>
  useUserSubscriptionStore((state) => state.canCreateMoreSquads)
export const useCanCreateMoreTripsInSquad = () =>
  useUserSubscriptionStore((state) => state.canCreateMoreTripsInSquad)

/**
 * Hook to handle subscription status changes and show appropriate toasts
 * This should be used in billing settings to handle success/cancellation toasts
 */
export const useSubscriptionStatusHandler = () => {
  const searchParams = useSearchParams()
  const isSubscribed = useIsUserSubscribed()

  const handleSubscriptionStatusChange = useCallback(() => {
    const success = searchParams.get("success")
    const sessionId = searchParams.get("session_id")
    const canceled = searchParams.get("canceled")

    // Check for successful subscription (either success=true or session_id present)
    if ((success === "true" || sessionId) && isSubscribed) {
      toast({
        title: "Subscription successful!",
        description: "Your subscription has been activated.",
      })
      return true // Indicates toast was shown
    } else if (canceled === "true") {
      toast({
        title: "Subscription canceled",
        description: "Your subscription process was canceled.",
        variant: "destructive",
      })
      return true // Indicates toast was shown
    }

    return false // No toast was shown
  }, [searchParams, isSubscribed])

  return { handleSubscriptionStatusChange }
}

/**
 * Enhanced user subscription hook with auto-initialization
 */
export const useUserSubscriptionWithInit = () => {
  const user = useUser()
  const router = useRouter()
  const store = useUserSubscriptionStore()
  const { fetchCurrentSubscription, refreshSubscriptionIfNeeded } = store

  // Fetch subscription data when auth state changes
  useEffect(() => {
    if (user?.uid) {
      fetchCurrentSubscription(user.uid)
    }
  }, [user, fetchCurrentSubscription])

  // Add a periodic refresh for very long sessions
  useEffect(() => {
    if (!user?.uid) return

    // Check when component mounts and when window regains focus
    refreshSubscriptionIfNeeded(user.uid)

    const handleFocus = () => {
      refreshSubscriptionIfNeeded(user.uid)
    }

    window.addEventListener("focus", handleFocus)
    return () => window.removeEventListener("focus", handleFocus)
  }, [user, refreshSubscriptionIfNeeded])

  // Create a router-aware error handler
  const handleSubscriptionError = (errorType: SubscriptionErrorType) => {
    store.handleSubscriptionErrorWithRouter(errorType, router)
  }

  // Return the store with the router-aware error handler
  return {
    ...store,
    handleSubscriptionError,
  }
}

/**
 * Hook for billing settings that fetches all subscription data
 */
export const useSubscriptionForBilling = () => {
  const user = useUser()
  const store = useUserSubscriptionStore()
  const { fetchCurrentSubscription, fetchAllSubscriptions, fetchSubscriptionSummary } = store

  // Fetch all subscription data when user changes
  useEffect(() => {
    if (user?.uid) {
      fetchCurrentSubscription(user.uid)
      fetchAllSubscriptions(user.uid)
      fetchSubscriptionSummary(user.uid)
    }
  }, [user, fetchCurrentSubscription, fetchAllSubscriptions, fetchSubscriptionSummary])

  return store
}
