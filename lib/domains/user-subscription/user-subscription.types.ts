import { Timestamp } from "firebase/firestore"
import { BaseEntity } from "../base/base.types"

/**
 * Subscription status type
 */
export type SubscriptionStatus =
  | "active"
  | "canceled"
  | "past_due"
  | "trialing"
  | "incomplete"
  | null

/**
 * Subscription plan type
 */
export type SubscriptionPlan = "free" | "monthly" | "yearly"

/**
 * Subscription source type
 */
export type SubscriptionSource = "stripe" | "perk" | "giveaway" | "free"

/**
 * Subscription entry status type
 */
export type SubscriptionEntryStatus = "applied" | "pending" | "paused" | "expired"

/**
 * Subscription precedence levels
 */
export const SUBSCRIPTION_PRECEDENCE = {
  PERK: 1,      // Highest priority
  GIVEAWAY: 2,  // Medium priority
  STRIPE: 3,    // Low priority
  FREE: 999,    // Lowest priority (default)
} as const

/**
 * Legacy User subscription entity (kept for backward compatibility during migration)
 * Will be removed after migration to flat collection structure
 */
export interface UserSubscription extends BaseEntity {
  userId: string
  stripeCustomerId: string
  subscriptionId: string
  subscriptionStatus: SubscriptionStatus
  subscriptionPlan: SubscriptionPlan
  subscriptionCurrentPeriodEnd: Timestamp | number | null
}

/**
 * Stripe-specific subscription data
 */
export interface StripeSubscriptionData {
  customerId: string
  subscriptionId: string
  subscriptionStatus: SubscriptionStatus
  subscriptionPlan: SubscriptionPlan // Moved here from entry level
  currentPeriodEnd: Timestamp | number
}

/**
 * Perk-specific subscription data
 */
export interface PerkSubscriptionData {
  perkId: string
  appliedAt: Timestamp
  duration: number // in days
}

/**
 * Giveaway-specific subscription data
 */
export interface GiveawaySubscriptionData {
  giveawayId: string
  duration: number // in days
}

/**
 * Free subscription data
 */
export interface FreeSubscriptionData {
  createdAt: Timestamp
}

/**
 * Union type for subscription data
 */
export type SubscriptionData =
  | StripeSubscriptionData
  | PerkSubscriptionData
  | GiveawaySubscriptionData
  | FreeSubscriptionData

/**
 * Free subscription data
 */
export interface FreeSubscriptionData {
  createdAt: Timestamp
}

/**
 * User subscription entry entity (flat collection structure)
 */
export interface UserSubscriptionEntry extends BaseEntity {
  userId: string // For querying by user in flat collection
  source: SubscriptionSource
  status: SubscriptionEntryStatus
  precedence: number

  // Subscription timing
  startDate: Timestamp
  endDate?: Timestamp // undefined for indefinite subscriptions (Stripe, free)

  // Union type for source-specific data
  subscriptionData: SubscriptionData

  // For pause/resume logic
  activeDays?: number // Days this subscription was active before being paused
  pausedAt?: Timestamp // When this subscription was paused
}

/**
 * User subscription creation data (legacy)
 */
export type UserSubscriptionCreateData = Omit<UserSubscription, "id" | "createdAt" | "updatedAt">

/**
 * User subscription entry creation data
 */
export type UserSubscriptionEntryCreateData = Omit<UserSubscriptionEntry, "id" | "createdAt" | "updatedAt">

/**
 * User subscription entry update data
 */
export type UserSubscriptionEntryUpdateData = Partial<Omit<UserSubscriptionEntry, "id" | "createdAt" | "userId">>

/**
 * Aggregated subscription state (computed from all entries)
 */
export interface AggregatedSubscriptionState {
  isSubscribed: boolean
  subscriptionPlan: SubscriptionPlan
  subscriptionStatus: SubscriptionStatus | null
  currentPeriodEnd: Timestamp | number | null
  activeEntry: UserSubscriptionEntry | null
  allEntries: UserSubscriptionEntry[]
  perkEnhancements: {
    additionalSquads: number
    additionalTripsPerSquad: number
    additionalDailyAI: number
    additionalWeeklyAI: number
    activePerkIds: string[]
  } | null
}

/**
 * Subscription entry creation helpers
 */
export const createStripeSubscriptionEntry = (
  userId: string,
  stripeData: StripeSubscriptionData
): UserSubscriptionEntryCreateData => {
  const entry: any = {
    userId,
    source: "stripe",
    status: "applied",
    precedence: SUBSCRIPTION_PRECEDENCE.STRIPE,
    startDate: new Date() as any, // Will be converted to Timestamp
    subscriptionData: stripeData,
  }
  // Don't include endDate for indefinite subscriptions to avoid Firestore undefined errors
  return entry
}

export const createPerkSubscriptionEntry = (
  userId: string,
  perkData: PerkSubscriptionData
): UserSubscriptionEntryCreateData => {
  const startDate = new Date()
  const endDate = new Date(startDate.getTime() + perkData.duration * 24 * 60 * 60 * 1000)

  return {
    userId,
    source: "perk",
    status: "applied",
    precedence: SUBSCRIPTION_PRECEDENCE.PERK,
    startDate: startDate as any,
    endDate: endDate as any,
    subscriptionData: perkData,
  }
}

export const createGiveawaySubscriptionEntry = (
  userId: string,
  giveawayData: GiveawaySubscriptionData
): UserSubscriptionEntryCreateData => {
  const startDate = new Date()
  const endDate = new Date(startDate.getTime() + giveawayData.duration * 24 * 60 * 60 * 1000)

  return {
    userId,
    source: "giveaway",
    status: "applied",
    precedence: SUBSCRIPTION_PRECEDENCE.GIVEAWAY,
    startDate: startDate as any,
    endDate: endDate as any,
    subscriptionData: giveawayData,
  }
}

export const createFreeSubscriptionEntry = (
  userId: string
): UserSubscriptionEntryCreateData => {
  const entry: any = {
    userId,
    source: "free",
    status: "applied",
    precedence: SUBSCRIPTION_PRECEDENCE.FREE,
    startDate: new Date() as any,
    subscriptionData: {
      createdAt: new Date() as any
    } as FreeSubscriptionData,
  }
  // Don't include endDate for indefinite subscriptions to avoid Firestore undefined errors
  return entry
}

/**
 * User subscription update data
 */
export type UserSubscriptionUpdateData = Partial<
  Omit<UserSubscription, "id" | "userId" | "createdAt">
>

/**
 * User subscriptions map
 * Maps user IDs to subscription status (true if subscribed)
 */
export interface UserSubscriptionsMap {
  [userId: string]: boolean
}

/**
 * Subscription Error Types
 */
export enum SubscriptionErrorType {
  MAX_SQUADS_REACHED = "MAX_SQUADS_REACHED",
  MAX_TRIPS_PER_SQUAD_REACHED = "MAX_TRIPS_PER_SQUAD_REACHED",
  DAILY_AI_LIMIT_REACHED = "DAILY_AI_LIMIT_REACHED",
  WEEKLY_AI_LIMIT_REACHED = "WEEKLY_AI_LIMIT_REACHED",
  TRIP_AI_LIMIT_REACHED = "TRIP_AI_LIMIT_REACHED",
  TASK_AI_LIMIT_REACHED = "TASK_AI_LIMIT_REACHED",
  ITINERARY_AI_LIMIT_REACHED = "ITINERARY_AI_LIMIT_REACHED",
  GENERIC_ERROR = "GENERIC_ERROR",
}

/**
 * Subscription limits
 */
export const SUBSCRIPTION_LIMITS = {
  FREE: {
    MAX_SQUADS: 1,
    MAX_TRIPS_PER_SQUAD: 2,
    MAX_DAILY_AI_REQUESTS: 10,
    MAX_WEEKLY_AI_REQUESTS: 50,
    HAS_TRIP_CHAT: false,
  },
  PRO: {
    MAX_SQUADS: 5,
    MAX_TRIPS_PER_SQUAD: 3,
    MAX_DAILY_AI_REQUESTS: Infinity,
    MAX_WEEKLY_AI_REQUESTS: Infinity,
    HAS_TRIP_CHAT: true,
  },
}

/**
 * Subscription plan IDs
 */
export const PLANS = {
  MONTHLY: process.env.NEXT_PUBLIC_STRIPE_PRICE_ID_MONTHLY!,
  YEARLY: process.env.NEXT_PUBLIC_STRIPE_PRICE_ID_YEARLY!,
}

/**
 * Subscription prices (for display purposes)
 */
export const SUBSCRIPTION_PRICES = {
  MONTHLY: 7.99,
  YEARLY: 79.99,
}

/**
 * Utility functions for subscription entries
 */

/**
 * Get precedence value for a subscription source
 */
export const getSourcePrecedence = (source: SubscriptionSource): number => {
  switch (source) {
    case "perk":
      return SUBSCRIPTION_PRECEDENCE.PERK
    case "giveaway":
      return SUBSCRIPTION_PRECEDENCE.GIVEAWAY
    case "stripe":
      return SUBSCRIPTION_PRECEDENCE.STRIPE
    default:
      return SUBSCRIPTION_PRECEDENCE.STRIPE
  }
}

/**
 * Check if a subscription entry is currently active
 */
export const isEntryActive = (entry: UserSubscriptionEntry): boolean => {
  const now = new Date()
  const startDate = entry.startDate instanceof Date ? entry.startDate : entry.startDate.toDate()
  const endDate = entry.endDate ?
    (entry.endDate instanceof Date ? entry.endDate : entry.endDate.toDate()) :
    null

  return (
    entry.status === "applied" &&
    startDate <= now &&
    (endDate === null || endDate > now)
  )
}

/**
 * Check if a subscription entry is expired
 */
export const isEntryExpired = (entry: UserSubscriptionEntry): boolean => {
  if (!entry.endDate) return false // Indefinite subscriptions don't expire

  const now = new Date()
  const endDate = entry.endDate instanceof Date ? entry.endDate : entry.endDate.toDate()

  return endDate <= now
}

/**
 * Sort subscription entries by precedence (highest first)
 */
export const sortEntriesByPrecedence = (entries: UserSubscriptionEntry[]): UserSubscriptionEntry[] => {
  return [...entries].sort((a, b) => a.precedence - b.precedence)
}

/**
 * Get the highest precedence active entry
 */
export const getActiveEntry = (entries: UserSubscriptionEntry[]): UserSubscriptionEntry | null => {
  const activeEntries = entries.filter(isEntryActive)
  const sortedEntries = sortEntriesByPrecedence(activeEntries)
  return sortedEntries[0] || null
}

/**
 * Validate subscription entry data
 */
export const validateSubscriptionEntry = (entry: UserSubscriptionEntryCreateData): string[] => {
  const errors: string[] = []

  if (!entry.userId) {
    errors.push("userId is required")
  }

  if (!["stripe", "perk", "giveaway", "free"].includes(entry.source)) {
    errors.push("Invalid subscription source")
  }

  if (!["applied", "pending", "paused", "expired"].includes(entry.status)) {
    errors.push("Invalid subscription status")
  }

  if (!entry.subscriptionData) {
    errors.push("subscriptionData is required")
  }

  // Validate source-specific data matches source type
  if (entry.source === "stripe") {
    const stripeData = entry.subscriptionData as StripeSubscriptionData
    if (!stripeData.customerId || !stripeData.subscriptionId) {
      errors.push("Stripe data must include customerId and subscriptionId")
    }
    if (!["monthly", "yearly"].includes(stripeData.subscriptionPlan)) {
      errors.push("Stripe subscription must have valid plan")
    }
  }

  if (entry.source === "perk") {
    const perkData = entry.subscriptionData as PerkSubscriptionData
    if (!perkData.perkId || !perkData.duration) {
      errors.push("Perk data must include perkId and duration")
    }
  }

  if (entry.source === "giveaway") {
    const giveawayData = entry.subscriptionData as GiveawaySubscriptionData
    if (!giveawayData.giveawayId || !giveawayData.duration) {
      errors.push("Giveaway data must include giveawayId and duration")
    }
  }

  if (entry.source === "free") {
    const freeData = entry.subscriptionData as FreeSubscriptionData
    if (!freeData.createdAt) {
      errors.push("Free data must include createdAt")
    }
  }

  return errors
}
