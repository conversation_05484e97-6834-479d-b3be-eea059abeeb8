import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest'
import { FlatSubscriptionService } from '../flat-subscription.service'
import { CleanMigrationService } from '../clean-migration.service'
import { FlatSubscriptionCronService } from '../flat-subscription-cron.service'
import {
  UserSubscriptionEntry,
  StripeSubscriptionData,
  PerkSubscriptionData,
  GiveawaySubscriptionData,
  createStripeSubscriptionEntry,
  createPerkSubscriptionEntry,
  createGiveawaySubscriptionEntry,
  createFreeSubscriptionEntry
} from '../user-subscription.types'

// Mock Firebase
vi.mock('@/lib/firebase', () => ({
  db: {},
}))

// Mock Firestore functions
vi.mock('firebase/firestore', () => ({
  collection: vi.fn(),
  doc: vi.fn(),
  getDocs: vi.fn(),
  getDoc: vi.fn(),
  setDoc: vi.fn(),
  updateDoc: vi.fn(),
  deleteDoc: vi.fn(),
  query: vi.fn(),
  where: vi.fn(),
  orderBy: vi.fn(),
  limit: vi.fn(),
  runTransaction: vi.fn(),
  serverTimestamp: vi.fn(() => new Date()),
  writeBatch: vi.fn(),
}))

describe('FlatSubscriptionService', () => {
  const mockUserId = 'test-user-123'
  const mockSubscriptionId = 'sub-123'

  beforeEach(() => {
    vi.clearAllMocks()
  })

  describe('Single User Subscription Queries', () => {
    it('should get current subscription for a user', async () => {
      // Mock Firestore response
      const mockSnapshot = {
        empty: false,
        docs: [{
          id: 'entry-123',
          data: () => ({
            userId: mockUserId,
            source: 'stripe',
            status: 'applied',
            precedence: 3,
            startDate: new Date(),
            subscriptionData: {
              customerId: 'cus-123',
              subscriptionId: mockSubscriptionId,
              subscriptionStatus: 'active',
              subscriptionPlan: 'monthly',
              currentPeriodEnd: new Date()
            }
          })
        }]
      }

      const { getDocs } = await import('firebase/firestore')
      vi.mocked(getDocs).mockResolvedValue(mockSnapshot as any)

      const result = await FlatSubscriptionService.getCurrentSubscription(mockUserId)

      expect(result).toBeTruthy()
      expect(result?.source).toBe('stripe')
      expect(result?.status).toBe('applied')
    })

    it('should return null when user has no current subscription', async () => {
      const mockSnapshot = { empty: true, docs: [] }

      const { getDocs } = await import('firebase/firestore')
      vi.mocked(getDocs).mockResolvedValue(mockSnapshot as any)

      const result = await FlatSubscriptionService.getCurrentSubscription(mockUserId)

      expect(result).toBeNull()
    })

    it('should get all subscriptions for a user', async () => {
      const mockSnapshot = {
        docs: [
          {
            id: 'entry-1',
            data: () => ({
              userId: mockUserId,
              source: 'stripe',
              status: 'applied',
              precedence: 3
            })
          },
          {
            id: 'entry-2',
            data: () => ({
              userId: mockUserId,
              source: 'free',
              status: 'paused',
              precedence: 999
            })
          }
        ]
      }

      const { getDocs } = await import('firebase/firestore')
      vi.mocked(getDocs).mockResolvedValue(mockSnapshot as any)

      const result = await FlatSubscriptionService.getUserSubscriptions(mockUserId)

      expect(result).toHaveLength(2)
      expect(result[0].source).toBe('stripe')
      expect(result[1].source).toBe('free')
    })
  })

  describe('Multi-User Queries (Squad Member Badges)', () => {
    it('should get subscriptions for multiple users', async () => {
      const userIds = ['user-1', 'user-2', 'user-3']
      const mockSnapshot = {
        docs: [
          {
            id: 'entry-1',
            data: () => ({
              userId: 'user-1',
              source: 'stripe',
              status: 'applied'
            })
          },
          {
            id: 'entry-2',
            data: () => ({
              userId: 'user-2',
              source: 'perk',
              status: 'applied'
            })
          },
          {
            id: 'entry-3',
            data: () => ({
              userId: 'user-3',
              source: 'free',
              status: 'applied'
            })
          }
        ]
      }

      const { getDocs } = await import('firebase/firestore')
      vi.mocked(getDocs).mockResolvedValue(mockSnapshot as any)

      const result = await FlatSubscriptionService.getMultiUserSubscriptions(userIds)

      expect(result).toHaveLength(3)
      expect(result.map(r => r.userId)).toEqual(['user-1', 'user-2', 'user-3'])
    })

    it('should handle empty user list', async () => {
      const result = await FlatSubscriptionService.getMultiUserSubscriptions([])
      expect(result).toEqual([])
    })

    it('should handle Firestore "in" query limitations (>10 users)', async () => {
      const userIds = Array.from({ length: 25 }, (_, i) => `user-${i}`)
      
      const { getDocs } = await import('firebase/firestore')
      vi.mocked(getDocs).mockResolvedValue({ docs: [] } as any)

      const result = await FlatSubscriptionService.getMultiUserSubscriptions(userIds)

      // Should make multiple queries due to Firestore 'in' limitation
      expect(getDocs).toHaveBeenCalledTimes(3) // 25 users = 3 chunks of 10
      expect(result).toEqual([])
    })
  })

  describe('Subscription Creation and Precedence', () => {
    it('should create Stripe subscription entry', async () => {
      const stripeData: StripeSubscriptionData = {
        customerId: 'cus-123',
        subscriptionId: mockSubscriptionId,
        subscriptionStatus: 'active',
        subscriptionPlan: 'monthly',
        currentPeriodEnd: new Date() as any
      }

      const { runTransaction } = await import('firebase/firestore')
      vi.mocked(runTransaction).mockImplementation(async (db, callback) => {
        return await callback({
          set: vi.fn(),
          update: vi.fn(),
          delete: vi.fn()
        } as any)
      })

      const result = await FlatSubscriptionService.addStripeSubscription(mockUserId, stripeData)

      expect(result.success).toBe(true)
      expect(runTransaction).toHaveBeenCalled()
    })

    it('should create perk subscription entry', async () => {
      const perkData: PerkSubscriptionData = {
        perkId: 'perk-123',
        perkName: 'Test Perk',
        grantedBy: 'admin-123',
        duration: 30
      }

      const { runTransaction } = await import('firebase/firestore')
      vi.mocked(runTransaction).mockImplementation(async (db, callback) => {
        return await callback({
          set: vi.fn(),
          update: vi.fn(),
          delete: vi.fn()
        } as any)
      })

      const result = await FlatSubscriptionService.addPerkSubscription(mockUserId, perkData)

      expect(result.success).toBe(true)
    })

    it('should create giveaway subscription entry', async () => {
      const giveawayData: GiveawaySubscriptionData = {
        giveawayId: 'giveaway-123',
        giveawayName: 'Test Giveaway',
        duration: 7
      }

      const { runTransaction } = await import('firebase/firestore')
      vi.mocked(runTransaction).mockImplementation(async (db, callback) => {
        return await callback({
          set: vi.fn(),
          update: vi.fn(),
          delete: vi.fn()
        } as any)
      })

      const result = await FlatSubscriptionService.addGiveawaySubscription(mockUserId, giveawayData)

      expect(result.success).toBe(true)
    })

    it('should ensure free subscription exists', async () => {
      // Mock no existing subscriptions
      const { getDocs, runTransaction } = await import('firebase/firestore')
      vi.mocked(getDocs).mockResolvedValue({ docs: [] } as any)
      vi.mocked(runTransaction).mockImplementation(async (db, callback) => {
        return await callback({
          set: vi.fn(),
          update: vi.fn(),
          delete: vi.fn()
        } as any)
      })

      const result = await FlatSubscriptionService.ensureFreeSubscription(mockUserId)

      expect(result.success).toBe(true)
    })
  })

  describe('Subscription Summary and Limits', () => {
    it('should get subscription summary', async () => {
      const mockSubscriptions = [
        { source: 'stripe', status: 'applied' },
        { source: 'perk', status: 'paused' },
        { source: 'free', status: 'paused' }
      ]

      // Mock getUserSubscriptions and getCurrentSubscription
      vi.spyOn(FlatSubscriptionService, 'getUserSubscriptions').mockResolvedValue(mockSubscriptions as any)
      vi.spyOn(FlatSubscriptionService, 'getCurrentSubscription').mockResolvedValue(mockSubscriptions[0] as any)

      const result = await FlatSubscriptionService.getSubscriptionSummary(mockUserId)

      expect(result.isSubscribed).toBe(true)
      expect(result.currentSource).toBe('stripe')
      expect(result.totalSubscriptions).toBe(3)
      expect(result.activeSubscriptions).toBe(1)
      expect(result.pausedSubscriptions).toBe(2)
    })

    it('should get subscription limits for pro user', async () => {
      const mockStripeSubscription = {
        source: 'stripe',
        subscriptionData: { subscriptionPlan: 'monthly' }
      }

      vi.spyOn(FlatSubscriptionService, 'getCurrentSubscription').mockResolvedValue(mockStripeSubscription as any)

      const result = await FlatSubscriptionService.getSubscriptionLimits(mockUserId)

      expect(result.maxSquads).toBe(5) // Pro limits
      expect(result.maxTripsPerSquad).toBe(10)
      expect(result.hasTripChat).toBe(true)
    })

    it('should get subscription limits for free user', async () => {
      const mockFreeSubscription = {
        source: 'free',
        subscriptionData: { createdAt: new Date() }
      }

      vi.spyOn(FlatSubscriptionService, 'getCurrentSubscription').mockResolvedValue(mockFreeSubscription as any)

      const result = await FlatSubscriptionService.getSubscriptionLimits(mockUserId)

      expect(result.maxSquads).toBe(1) // Free limits
      expect(result.maxTripsPerSquad).toBe(2)
      expect(result.hasTripChat).toBe(false)
    })

    it('should check feature access', async () => {
      const mockStripeSubscription = { source: 'stripe' }
      vi.spyOn(FlatSubscriptionService, 'getCurrentSubscription').mockResolvedValue(mockStripeSubscription as any)

      const tripChatAccess = await FlatSubscriptionService.hasFeatureAccess(mockUserId, 'trip_chat')
      const unlimitedAIAccess = await FlatSubscriptionService.hasFeatureAccess(mockUserId, 'unlimited_ai')

      expect(tripChatAccess).toBe(true)
      expect(unlimitedAIAccess).toBe(true)
    })
  })

  describe('Helper Functions', () => {
    it('should create Stripe subscription entry with correct precedence', () => {
      const stripeData: StripeSubscriptionData = {
        customerId: 'cus-123',
        subscriptionId: 'sub-123',
        subscriptionStatus: 'active',
        subscriptionPlan: 'yearly',
        currentPeriodEnd: new Date() as any
      }

      const entry = createStripeSubscriptionEntry(mockUserId, stripeData)

      expect(entry.userId).toBe(mockUserId)
      expect(entry.source).toBe('stripe')
      expect(entry.precedence).toBe(3) // Stripe precedence
      expect(entry.status).toBe('applied')
      expect(entry.subscriptionData).toEqual(stripeData)
    })

    it('should create perk subscription entry with highest precedence', () => {
      const perkData: PerkSubscriptionData = {
        perkId: 'perk-123',
        perkName: 'Test Perk',
        grantedBy: 'admin-123',
        duration: 30
      }

      const entry = createPerkSubscriptionEntry(mockUserId, perkData)

      expect(entry.source).toBe('perk')
      expect(entry.precedence).toBe(1) // Highest precedence
      expect(entry.status).toBe('applied')
    })

    it('should create free subscription entry with lowest precedence', () => {
      const entry = createFreeSubscriptionEntry(mockUserId)

      expect(entry.source).toBe('free')
      expect(entry.precedence).toBe(999) // Lowest precedence
      expect(entry.status).toBe('applied')
      expect(entry.endDate).toBeUndefined() // Free subscriptions don't expire
    })
  })
})

describe('CleanMigrationService', () => {
  const mockUserId = 'test-user-123'

  beforeEach(() => {
    vi.clearAllMocks()
  })

  describe('Migration Functions', () => {
    it('should backup existing data', async () => {
      const mockSnapshot = {
        docs: [
          {
            id: 'user-1',
            data: () => ({ subscriptionPlan: 'monthly', subscriptionStatus: 'active' })
          }
        ]
      }

      const { getDocs, writeBatch } = await import('firebase/firestore')
      vi.mocked(getDocs).mockResolvedValue(mockSnapshot as any)
      
      const mockBatch = {
        set: vi.fn(),
        commit: vi.fn().mockResolvedValue(undefined)
      }
      vi.mocked(writeBatch).mockReturnValue(mockBatch as any)

      const result = await CleanMigrationService.backupExistingData()

      expect(result.success).toBe(true)
      expect(result.data?.backedUpCount).toBe(1)
      expect(mockBatch.commit).toHaveBeenCalled()
    })

    it('should migrate single user subscription', async () => {
      const mockSubscriptionDoc = {
        exists: () => true,
        data: () => ({
          stripeCustomerId: 'cus-123',
          subscriptionId: 'sub-123',
          subscriptionPlan: 'monthly',
          subscriptionStatus: 'active'
        })
      }

      const { getDoc, runTransaction } = await import('firebase/firestore')
      vi.mocked(getDoc).mockResolvedValue(mockSubscriptionDoc as any)
      vi.mocked(runTransaction).mockImplementation(async (db, callback) => {
        return await callback({
          set: vi.fn(),
          update: vi.fn(),
          delete: vi.fn()
        } as any)
      })

      const result = await CleanMigrationService.migrateUserSubscription(mockUserId)

      expect(result.success).toBe(true)
      expect(result.data?.migrated).toBe(true)
      expect(result.data?.entriesCreated).toBe(2) // Stripe + Free
    })

    it('should validate migration results', async () => {
      const mockUsersSnapshot = {
        size: 10,
        docs: Array.from({ length: 10 }, (_, i) => ({ id: `user-${i}` }))
      }

      const mockEntriesSnapshot = {
        size: 20,
        docs: Array.from({ length: 20 }, (_, i) => ({
          id: `entry-${i}`,
          data: () => ({
            userId: `user-${i % 10}`,
            source: i % 2 === 0 ? 'free' : 'stripe',
            status: 'applied'
          })
        }))
      }

      const { getDocs } = await import('firebase/firestore')
      vi.mocked(getDocs)
        .mockResolvedValueOnce(mockUsersSnapshot as any)
        .mockResolvedValueOnce(mockEntriesSnapshot as any)

      const result = await CleanMigrationService.validateMigration()

      expect(result.success).toBe(true)
      expect(result.data?.statistics.totalUsers).toBe(10)
      expect(result.data?.statistics.totalEntries).toBe(20)
    })
  })
})

describe('Pause/Resume Logic with activeDays', () => {
  const mockUserId = 'test-user-123'

  beforeEach(() => {
    vi.clearAllMocks()
  })

  describe('Subscription Precedence and Pausing', () => {
    it('should pause lower precedence subscription when higher precedence is added', async () => {
      // Mock existing Stripe subscription (precedence 3)
      const existingStripeSubscription = {
        id: 'stripe-entry',
        source: 'stripe',
        status: 'applied',
        precedence: 3,
        startDate: new Date(Date.now() - 10 * 24 * 60 * 60 * 1000), // 10 days ago
        subscriptionData: {
          customerId: 'cus-123',
          subscriptionId: 'sub-123',
          subscriptionStatus: 'active',
          subscriptionPlan: 'monthly'
        }
      }

      vi.spyOn(FlatSubscriptionService, 'getCurrentSubscription').mockResolvedValue(existingStripeSubscription as any)

      const { runTransaction } = await import('firebase/firestore')
      const mockTransaction = {
        set: vi.fn(),
        update: vi.fn(),
        delete: vi.fn()
      }
      vi.mocked(runTransaction).mockImplementation(async (db, callback) => {
        return await callback(mockTransaction as any)
      })

      // Add perk subscription (precedence 1 - higher than Stripe)
      const perkData: PerkSubscriptionData = {
        perkId: 'perk-123',
        perkName: 'Test Perk',
        grantedBy: 'admin-123',
        duration: 30
      }

      const result = await FlatSubscriptionService.addPerkSubscription(mockUserId, perkData)

      expect(result.success).toBe(true)
      expect(mockTransaction.update).toHaveBeenCalledWith(
        expect.anything(),
        expect.objectContaining({
          status: 'paused',
          pausedAt: expect.any(Date),
          activeDays: 10 // Should calculate 10 days active
        })
      )
    })

    it('should set new subscription as pending when existing has higher precedence', async () => {
      // Mock existing perk subscription (precedence 1)
      const existingPerkSubscription = {
        id: 'perk-entry',
        source: 'perk',
        status: 'applied',
        precedence: 1
      }

      vi.spyOn(FlatSubscriptionService, 'getCurrentSubscription').mockResolvedValue(existingPerkSubscription as any)

      const { runTransaction } = await import('firebase/firestore')
      vi.mocked(runTransaction).mockImplementation(async (db, callback) => {
        return await callback({
          set: vi.fn(),
          update: vi.fn(),
          delete: vi.fn()
        } as any)
      })

      // Try to add Stripe subscription (precedence 3 - lower than perk)
      const stripeData: StripeSubscriptionData = {
        customerId: 'cus-123',
        subscriptionId: 'sub-123',
        subscriptionStatus: 'active',
        subscriptionPlan: 'monthly',
        currentPeriodEnd: new Date() as any
      }

      const result = await FlatSubscriptionService.addStripeSubscription(mockUserId, stripeData)

      expect(result.success).toBe(true)
      // The new Stripe subscription should be created with 'pending' status
    })

    it('should reactivate paused subscription when higher precedence expires', async () => {
      const mockUserSubscriptions = [
        {
          id: 'perk-entry',
          source: 'perk',
          status: 'applied',
          precedence: 1,
          endDate: new Date(Date.now() - 1000) // Expired 1 second ago
        },
        {
          id: 'stripe-entry',
          source: 'stripe',
          status: 'paused',
          precedence: 3,
          activeDays: 15,
          pausedAt: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000) // Paused 5 days ago
        }
      ]

      vi.spyOn(FlatSubscriptionService, 'getUserSubscriptions').mockResolvedValue(mockUserSubscriptions as any)

      const { runTransaction } = await import('firebase/firestore')
      const mockTransaction = {
        update: vi.fn()
      }
      vi.mocked(runTransaction).mockImplementation(async (db, callback) => {
        return await callback(mockTransaction as any)
      })

      const result = await FlatSubscriptionService.processExpiredSubscriptions(mockUserId)

      expect(result.success).toBe(true)
      expect(result.data?.expiredCount).toBe(1)
      expect(result.data?.reactivatedSubscription).toBeTruthy()

      // Should reactivate the Stripe subscription with remaining time
      expect(mockTransaction.update).toHaveBeenCalledWith(
        expect.anything(),
        expect.objectContaining({
          status: 'applied',
          endDate: expect.any(Date) // Should calculate new end date based on remaining days
        })
      )
    })
  })

  describe('activeDays Calculation', () => {
    it('should calculate active days correctly', () => {
      const startDate = new Date(Date.now() - 10 * 24 * 60 * 60 * 1000) // 10 days ago
      const subscription = {
        id: 'test-entry',
        startDate: startDate as any,
        source: 'stripe',
        status: 'applied',
        precedence: 3
      }

      // Access private method for testing
      const activeDays = (FlatSubscriptionService as any).calculateActiveDays(subscription)

      expect(activeDays).toBe(10)
    })

    it('should calculate remaining days for paused subscription', () => {
      const startDate = new Date(Date.now() - 20 * 24 * 60 * 60 * 1000) // 20 days ago
      const endDate = new Date(Date.now() + 10 * 24 * 60 * 60 * 1000) // 10 days from now (30 day total)
      const subscription = {
        id: 'test-entry',
        startDate: startDate as any,
        endDate: endDate as any,
        activeDays: 15, // Was active for 15 days before being paused
        source: 'stripe',
        status: 'paused',
        precedence: 3
      }

      // Access private method for testing
      const remainingDays = (FlatSubscriptionService as any).calculateRemainingDays(subscription)

      expect(remainingDays).toBe(15) // 30 total - 15 active = 15 remaining
    })
  })
})

describe('FlatSubscriptionCronService', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  describe('Cron Jobs', () => {
    it('should process subscription expirations', async () => {
      // Mock getUsersWithSubscriptions
      vi.spyOn(FlatSubscriptionCronService as any, 'getUsersWithSubscriptions').mockResolvedValue(['user-1', 'user-2'])

      // Mock processExpiredSubscriptions
      vi.spyOn(FlatSubscriptionService, 'processExpiredSubscriptions').mockResolvedValue({
        success: true,
        data: { expiredCount: 1, reactivatedSubscription: null }
      })

      const result = await FlatSubscriptionCronService.processSubscriptionExpirations()

      expect(result.success).toBe(true)
      expect(result.data?.processedUsers).toBe(2)
      expect(result.data?.expiredEntries).toBe(2) // 1 per user
    })

    it('should perform health check', async () => {
      // Mock getUsersWithSubscriptions
      vi.spyOn(FlatSubscriptionCronService as any, 'getUsersWithSubscriptions').mockResolvedValue(['user-1'])

      // Mock checkUserSubscriptionHealth
      vi.spyOn(FlatSubscriptionCronService as any, 'checkUserSubscriptionHealth').mockResolvedValue([])

      const result = await FlatSubscriptionCronService.performHealthCheck()

      expect(result.success).toBe(true)
      expect(result.data?.systemHealth).toBe('healthy')
    })

    it('should cleanup expired entries older than 30 days', async () => {
      const oldDate = new Date(Date.now() - 35 * 24 * 60 * 60 * 1000) // 35 days ago
      const mockSnapshot = {
        docs: [
          {
            id: 'expired-entry-1',
            data: () => ({
              userId: 'user-1',
              status: 'expired',
              updatedAt: oldDate
            })
          }
        ]
      }

      const { getDocs } = await import('firebase/firestore')
      vi.mocked(getDocs).mockResolvedValue(mockSnapshot as any)

      vi.spyOn(FlatSubscriptionService, 'deleteSubscriptionEntry').mockResolvedValue({ success: true })

      const result = await FlatSubscriptionCronService.cleanupExpiredEntries()

      expect(result.success).toBe(true)
      expect(result.data?.cleanedEntries).toBe(1)
    })
  })
})
