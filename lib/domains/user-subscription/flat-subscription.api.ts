import { NextRequest, NextResponse } from "next/server"
import { FlatSubscriptionService } from "./flat-subscription.service"
import { FlatSubscriptionCronService } from "./flat-subscription-cron.service"
import { 
  UserSubscriptionEntryCreateData,
  UserSubscriptionEntryUpdateData,
  StripeSubscriptionData,
  PerkSubscriptionData,
  GiveawaySubscriptionData
} from "./user-subscription.types"

/**
 * API handlers for flat subscription system
 */
export class FlatSubscriptionAPI {

  /**
   * GET /api/subscriptions/current?userId=<userId>
   * Get current subscription for a user
   */
  static async getCurrentSubscription(request: NextRequest): Promise<NextResponse> {
    try {
      const { searchParams } = new URL(request.url)
      const userId = searchParams.get("userId")

      if (!userId) {
        return NextResponse.json(
          { error: "userId parameter is required" },
          { status: 400 }
        )
      }

      const currentSubscription = await FlatSubscriptionService.getCurrentSubscription(userId)
      
      return NextResponse.json({
        success: true,
        data: currentSubscription
      })
    } catch (error) {
      console.error("Error getting current subscription:", error)
      return NextResponse.json(
        { error: "Internal server error" },
        { status: 500 }
      )
    }
  }

  /**
   * GET /api/subscriptions/all?userId=<userId>
   * Get all subscriptions for a user
   */
  static async getAllSubscriptions(request: NextRequest): Promise<NextResponse> {
    try {
      const { searchParams } = new URL(request.url)
      const userId = searchParams.get("userId")

      if (!userId) {
        return NextResponse.json(
          { error: "userId parameter is required" },
          { status: 400 }
        )
      }

      const allSubscriptions = await FlatSubscriptionService.getUserSubscriptions(userId)
      
      return NextResponse.json({
        success: true,
        data: allSubscriptions
      })
    } catch (error) {
      console.error("Error getting all subscriptions:", error)
      return NextResponse.json(
        { error: "Internal server error" },
        { status: 500 }
      )
    }
  }

  /**
   * POST /api/subscriptions/multi-user
   * Get subscriptions for multiple users (squad member badges)
   */
  static async getMultiUserSubscriptions(request: NextRequest): Promise<NextResponse> {
    try {
      const body = await request.json()
      const { userIds } = body

      if (!Array.isArray(userIds) || userIds.length === 0) {
        return NextResponse.json(
          { error: "userIds array is required" },
          { status: 400 }
        )
      }

      const subscriptions = await FlatSubscriptionService.getMultiUserSubscriptions(userIds)
      
      return NextResponse.json({
        success: true,
        data: subscriptions
      })
    } catch (error) {
      console.error("Error getting multi-user subscriptions:", error)
      return NextResponse.json(
        { error: "Internal server error" },
        { status: 500 }
      )
    }
  }

  /**
   * GET /api/subscriptions/summary?userId=<userId>
   * Get subscription summary for a user
   */
  static async getSubscriptionSummary(request: NextRequest): Promise<NextResponse> {
    try {
      const { searchParams } = new URL(request.url)
      const userId = searchParams.get("userId")

      if (!userId) {
        return NextResponse.json(
          { error: "userId parameter is required" },
          { status: 400 }
        )
      }

      const summary = await FlatSubscriptionService.getSubscriptionSummary(userId)
      
      return NextResponse.json({
        success: true,
        data: summary
      })
    } catch (error) {
      console.error("Error getting subscription summary:", error)
      return NextResponse.json(
        { error: "Internal server error" },
        { status: 500 }
      )
    }
  }

  /**
   * GET /api/subscriptions/limits?userId=<userId>
   * Get subscription limits for a user
   */
  static async getSubscriptionLimits(request: NextRequest): Promise<NextResponse> {
    try {
      const { searchParams } = new URL(request.url)
      const userId = searchParams.get("userId")

      if (!userId) {
        return NextResponse.json(
          { error: "userId parameter is required" },
          { status: 400 }
        )
      }

      const limits = await FlatSubscriptionService.getSubscriptionLimits(userId)
      
      return NextResponse.json({
        success: true,
        data: limits
      })
    } catch (error) {
      console.error("Error getting subscription limits:", error)
      return NextResponse.json(
        { error: "Internal server error" },
        { status: 500 }
      )
    }
  }

  /**
   * GET /api/subscriptions/feature-access?userId=<userId>&feature=<feature>
   * Check if user has access to a specific feature
   */
  static async checkFeatureAccess(request: NextRequest): Promise<NextResponse> {
    try {
      const { searchParams } = new URL(request.url)
      const userId = searchParams.get("userId")
      const feature = searchParams.get("feature")

      if (!userId || !feature) {
        return NextResponse.json(
          { error: "userId and feature parameters are required" },
          { status: 400 }
        )
      }

      const hasAccess = await FlatSubscriptionService.hasFeatureAccess(userId, feature)
      
      return NextResponse.json({
        success: true,
        data: { hasAccess }
      })
    } catch (error) {
      console.error("Error checking feature access:", error)
      return NextResponse.json(
        { error: "Internal server error" },
        { status: 500 }
      )
    }
  }

  /**
   * POST /api/subscriptions/stripe
   * Add Stripe subscription
   */
  static async addStripeSubscription(request: NextRequest): Promise<NextResponse> {
    try {
      const body = await request.json()
      const { userId, stripeData } = body

      if (!userId || !stripeData) {
        return NextResponse.json(
          { error: "userId and stripeData are required" },
          { status: 400 }
        )
      }

      const result = await FlatSubscriptionService.addStripeSubscription(userId, stripeData as StripeSubscriptionData)
      
      if (result.success) {
        return NextResponse.json({
          success: true,
          data: result.data
        })
      } else {
        return NextResponse.json(
          { error: result.error?.message || "Failed to add Stripe subscription" },
          { status: 400 }
        )
      }
    } catch (error) {
      console.error("Error adding Stripe subscription:", error)
      return NextResponse.json(
        { error: "Internal server error" },
        { status: 500 }
      )
    }
  }

  /**
   * POST /api/subscriptions/perk
   * Add perk subscription
   */
  static async addPerkSubscription(request: NextRequest): Promise<NextResponse> {
    try {
      const body = await request.json()
      const { userId, perkData } = body

      if (!userId || !perkData) {
        return NextResponse.json(
          { error: "userId and perkData are required" },
          { status: 400 }
        )
      }

      const result = await FlatSubscriptionService.addPerkSubscription(userId, perkData as PerkSubscriptionData)
      
      if (result.success) {
        return NextResponse.json({
          success: true,
          data: result.data
        })
      } else {
        return NextResponse.json(
          { error: result.error?.message || "Failed to add perk subscription" },
          { status: 400 }
        )
      }
    } catch (error) {
      console.error("Error adding perk subscription:", error)
      return NextResponse.json(
        { error: "Internal server error" },
        { status: 500 }
      )
    }
  }

  /**
   * POST /api/subscriptions/giveaway
   * Add giveaway subscription
   */
  static async addGiveawaySubscription(request: NextRequest): Promise<NextResponse> {
    try {
      const body = await request.json()
      const { userId, giveawayData } = body

      if (!userId || !giveawayData) {
        return NextResponse.json(
          { error: "userId and giveawayData are required" },
          { status: 400 }
        )
      }

      const result = await FlatSubscriptionService.addGiveawaySubscription(userId, giveawayData as GiveawaySubscriptionData)
      
      if (result.success) {
        return NextResponse.json({
          success: true,
          data: result.data
        })
      } else {
        return NextResponse.json(
          { error: result.error?.message || "Failed to add giveaway subscription" },
          { status: 400 }
        )
      }
    } catch (error) {
      console.error("Error adding giveaway subscription:", error)
      return NextResponse.json(
        { error: "Internal server error" },
        { status: 500 }
      )
    }
  }

  /**
   * POST /api/subscriptions/ensure-free
   * Ensure user has a free subscription
   */
  static async ensureFreeSubscription(request: NextRequest): Promise<NextResponse> {
    try {
      const body = await request.json()
      const { userId } = body

      if (!userId) {
        return NextResponse.json(
          { error: "userId is required" },
          { status: 400 }
        )
      }

      const result = await FlatSubscriptionService.ensureFreeSubscription(userId)
      
      if (result.success) {
        return NextResponse.json({
          success: true,
          message: "Free subscription ensured"
        })
      } else {
        return NextResponse.json(
          { error: result.error?.message || "Failed to ensure free subscription" },
          { status: 400 }
        )
      }
    } catch (error) {
      console.error("Error ensuring free subscription:", error)
      return NextResponse.json(
        { error: "Internal server error" },
        { status: 500 }
      )
    }
  }

  /**
   * POST /api/cron/process-expirations
   * Cron job to process subscription expirations
   */
  static async processExpirations(request: NextRequest): Promise<NextResponse> {
    try {
      const result = await FlatSubscriptionCronService.processSubscriptionExpirations()
      
      if (result.success) {
        return NextResponse.json({
          success: true,
          data: result.data
        })
      } else {
        return NextResponse.json(
          { error: result.error?.message || "Failed to process expirations" },
          { status: 500 }
        )
      }
    } catch (error) {
      console.error("Error processing expirations:", error)
      return NextResponse.json(
        { error: "Internal server error" },
        { status: 500 }
      )
    }
  }

  /**
   * POST /api/cron/cleanup-expired
   * Cron job to cleanup expired entries
   */
  static async cleanupExpired(request: NextRequest): Promise<NextResponse> {
    try {
      const result = await FlatSubscriptionCronService.cleanupExpiredEntries()
      
      if (result.success) {
        return NextResponse.json({
          success: true,
          data: result.data
        })
      } else {
        return NextResponse.json(
          { error: result.error?.message || "Failed to cleanup expired entries" },
          { status: 500 }
        )
      }
    } catch (error) {
      console.error("Error cleaning up expired entries:", error)
      return NextResponse.json(
        { error: "Internal server error" },
        { status: 500 }
      )
    }
  }

  /**
   * GET /api/cron/health-check
   * Health check for subscription system
   */
  static async healthCheck(request: NextRequest): Promise<NextResponse> {
    try {
      const result = await FlatSubscriptionCronService.performHealthCheck()
      
      if (result.success) {
        return NextResponse.json({
          success: true,
          data: result.data
        })
      } else {
        return NextResponse.json(
          { error: result.error?.message || "Health check failed" },
          { status: 500 }
        )
      }
    } catch (error) {
      console.error("Error performing health check:", error)
      return NextResponse.json(
        { error: "Internal server error" },
        { status: 500 }
      )
    }
  }
}
