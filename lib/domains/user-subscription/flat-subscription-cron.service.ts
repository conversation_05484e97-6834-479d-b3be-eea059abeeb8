import { collection, getDocs, doc, setDoc, serverTimestamp, query, where } from "firebase/firestore"
import { db } from "@/lib/firebase"
import { ServiceResponse } from "../base/base.types"
import { FlatSubscriptionService } from "./flat-subscription.service"
import { UserSubscriptionEntry } from "./user-subscription.types"

/**
 * Cron service for flat subscription structure with activeDays pause/resume logic
 */
export class FlatSubscriptionCronService {
  private static readonly CRON_LOG_COLLECTION = "cronLogs"
  private static readonly BATCH_SIZE = 50 // Process users in batches

  /**
   * Main cron job to process subscription expirations with activeDays logic
   */
  static async processSubscriptionExpirations(): Promise<
    ServiceResponse<{
      processedUsers: number
      expiredEntries: number
      reactivatedEntries: number
      errors: string[]
      duration: number
    }>
  > {
    const startTime = Date.now()

    try {
      console.log("🕐 Starting flat subscription expiration processing...")

      const errors: string[] = []
      let processedUsers = 0
      let expiredEntries = 0
      let reactivatedEntries = 0

      // Get all unique user IDs with subscriptions
      const usersWithSubscriptions = await this.getUsersWithSubscriptions()

      console.log(`📊 Found ${usersWithSubscriptions.length} users with subscriptions`)

      // Process users in batches
      for (let i = 0; i < usersWithSubscriptions.length; i += this.BATCH_SIZE) {
        const batch = usersWithSubscriptions.slice(i, i + this.BATCH_SIZE)

        for (const userId of batch) {
          try {
            const result = await FlatSubscriptionService.processExpiredSubscriptions(userId)

            processedUsers++

            if (result.success) {
              expiredEntries += result.data!.expiredCount
              if (result.data!.reactivatedSubscription) {
                reactivatedEntries++
              }
            } else {
              const errorMessage =
                typeof result.error === "string"
                  ? result.error
                  : (result.error as any)?.message || "Unknown error"
              errors.push(`User ${userId}: ${errorMessage}`)
            }
          } catch (error) {
            const errorMsg = `Failed to process user ${userId}: ${error}`
            console.error(errorMsg)
            errors.push(errorMsg)
          }
        }

        // Small delay between batches to avoid overwhelming the database
        if (i + this.BATCH_SIZE < usersWithSubscriptions.length) {
          await new Promise((resolve) => setTimeout(resolve, 100))
        }
      }

      const duration = Date.now() - startTime

      // Log cron job execution
      await this.logCronExecution("flat_subscription_expiration", {
        processedUsers,
        expiredEntries,
        reactivatedEntries,
        errorCount: errors.length,
        duration,
      })

      console.log(`✅ Flat subscription expiration processing completed in ${duration}ms`)
      console.log(
        `📊 Processed: ${processedUsers} users, ${expiredEntries} expired, ${reactivatedEntries} reactivated`
      )

      if (errors.length > 0) {
        console.log(`⚠️  ${errors.length} errors occurred`)
      }

      return {
        success: true,
        data: {
          processedUsers,
          expiredEntries,
          reactivatedEntries,
          errors,
          duration,
        },
      }
    } catch (error) {
      const duration = Date.now() - startTime
      console.error("❌ Flat subscription expiration processing failed:", error)

      await this.logCronExecution("flat_subscription_expiration", {
        processedUsers: 0,
        expiredEntries: 0,
        reactivatedEntries: 0,
        errorCount: 1,
        duration,
        error: error instanceof Error ? error.message : "Unknown error",
      })

      return { success: false, error }
    }
  }

  /**
   * Cleanup expired subscription entries (run weekly)
   */
  static async cleanupExpiredEntries(): Promise<
    ServiceResponse<{
      processedUsers: number
      cleanedEntries: number
      errors: string[]
      duration: number
    }>
  > {
    const startTime = Date.now()

    try {
      console.log("🧹 Starting expired subscription entries cleanup...")

      const errors: string[] = []
      let processedUsers = 0
      let cleanedEntries = 0

      // Get all expired entries older than 30 days
      const thirtyDaysAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)
      const expiredEntriesQuery = query(
        collection(db, "userSubscriptions"),
        where("status", "==", "expired")
      )

      const expiredSnapshot = await getDocs(expiredEntriesQuery)
      const userGroups = new Map<string, UserSubscriptionEntry[]>()

      // Group expired entries by user
      for (const doc of expiredSnapshot.docs) {
        const entry = { id: doc.id, ...doc.data() } as UserSubscriptionEntry
        const updatedAt =
          entry.updatedAt instanceof Date ? entry.updatedAt : entry.updatedAt?.toDate()

        // Only process entries older than 30 days
        if (updatedAt && updatedAt < thirtyDaysAgo) {
          if (!userGroups.has(entry.userId)) {
            userGroups.set(entry.userId, [])
          }
          userGroups.get(entry.userId)!.push(entry)
        }
      }

      // Process cleanup for each user
      for (const [userId, expiredEntries] of userGroups) {
        try {
          processedUsers++

          for (const entry of expiredEntries) {
            const deleteResult = await FlatSubscriptionService.deleteSubscriptionEntry(entry.id)
            if (deleteResult.success) {
              cleanedEntries++
            } else {
              errors.push(`Failed to delete entry ${entry.id} for user ${userId}`)
            }
          }
        } catch (error) {
          errors.push(`Failed to cleanup entries for user ${userId}: ${error}`)
        }
      }

      const duration = Date.now() - startTime

      await this.logCronExecution("cleanup_expired_entries", {
        processedUsers,
        cleanedEntries,
        errorCount: errors.length,
        duration,
      })

      console.log(
        `✅ Cleanup completed: ${cleanedEntries} entries cleaned from ${processedUsers} users`
      )

      return {
        success: true,
        data: {
          processedUsers,
          cleanedEntries,
          errors,
          duration,
        },
      }
    } catch (error) {
      const duration = Date.now() - startTime
      console.error("❌ Cleanup failed:", error)

      await this.logCronExecution("cleanup_expired_entries", {
        processedUsers: 0,
        cleanedEntries: 0,
        errorCount: 1,
        duration,
        error: error instanceof Error ? error.message : "Unknown error",
      })

      return { success: false, error }
    }
  }

  /**
   * Health check for flat subscription system
   */
  static async performHealthCheck(): Promise<
    ServiceResponse<{
      totalUsers: number
      usersWithIssues: number
      commonIssues: Array<{
        issue: string
        count: number
      }>
      systemHealth: "healthy" | "warning" | "critical"
    }>
  > {
    try {
      console.log("🏥 Performing flat subscription system health check...")

      const usersWithSubscriptions = await this.getUsersWithSubscriptions()
      const issueMap = new Map<string, number>()
      let usersWithIssues = 0

      // Sample a subset of users for health check to avoid performance issues
      const sampleSize = Math.min(100, usersWithSubscriptions.length)
      const sampleUsers = usersWithSubscriptions.slice(0, sampleSize)

      for (const userId of sampleUsers) {
        try {
          const issues = await this.checkUserSubscriptionHealth(userId)

          if (issues.length > 0) {
            usersWithIssues++

            // Count common issues
            issues.forEach((issue) => {
              const count = issueMap.get(issue) || 0
              issueMap.set(issue, count + 1)
            })
          }
        } catch (error) {
          usersWithIssues++
          const issue = "Health check failed"
          const count = issueMap.get(issue) || 0
          issueMap.set(issue, count + 1)
        }
      }

      const commonIssues = Array.from(issueMap.entries())
        .map(([issue, count]) => ({ issue, count }))
        .sort((a, b) => b.count - a.count)

      // Determine system health
      const issueRate = usersWithIssues / sampleUsers.length
      let systemHealth: "healthy" | "warning" | "critical"

      if (issueRate < 0.05) {
        systemHealth = "healthy"
      } else if (issueRate < 0.2) {
        systemHealth = "warning"
      } else {
        systemHealth = "critical"
      }

      await this.logCronExecution("health_check", {
        totalUsers: usersWithSubscriptions.length,
        sampleSize,
        usersWithIssues,
        issueRate,
        systemHealth,
      })

      console.log(
        `🏥 Health check completed: ${systemHealth} (${usersWithIssues}/${sampleSize} users with issues)`
      )

      return {
        success: true,
        data: {
          totalUsers: usersWithSubscriptions.length,
          usersWithIssues,
          commonIssues,
          systemHealth,
        },
      }
    } catch (error) {
      console.error("❌ Health check failed:", error)
      return { success: false, error }
    }
  }

  /**
   * Check subscription health for a single user
   */
  private static async checkUserSubscriptionHealth(userId: string): Promise<string[]> {
    const issues: string[] = []

    try {
      const allSubscriptions = await FlatSubscriptionService.getUserSubscriptions(userId)
      const currentSubscription = await FlatSubscriptionService.getCurrentSubscription(userId)

      // Check if user has a free subscription
      const hasFreeSubscription = allSubscriptions.some((sub) => sub.source === "free")
      if (!hasFreeSubscription) {
        issues.push("User missing free subscription")
      }

      // Check if user has multiple applied subscriptions
      const appliedSubscriptions = allSubscriptions.filter((sub) => sub.status === "applied")
      if (appliedSubscriptions.length > 1) {
        issues.push("User has multiple applied subscriptions")
      }

      // Check if user has no applied subscription
      if (appliedSubscriptions.length === 0) {
        issues.push("User has no applied subscription")
      }

      // Check for inconsistent current subscription
      if (
        currentSubscription &&
        !appliedSubscriptions.find((sub) => sub.id === currentSubscription.id)
      ) {
        issues.push("Current subscription not in applied list")
      }
    } catch (error) {
      issues.push("Failed to check user subscription health")
    }

    return issues
  }

  /**
   * Get list of users with subscription entries
   */
  private static async getUsersWithSubscriptions(): Promise<string[]> {
    try {
      // Get all subscription documents and extract unique user IDs
      const subscriptionsSnapshot = await getDocs(collection(db, "userSubscriptions"))
      const userIds = new Set<string>()

      for (const doc of subscriptionsSnapshot.docs) {
        const entry = doc.data() as UserSubscriptionEntry
        userIds.add(entry.userId)
      }

      return Array.from(userIds)
    } catch (error) {
      console.error("Error getting users with subscriptions:", error)
      return []
    }
  }

  /**
   * Log cron job execution
   */
  private static async logCronExecution(jobType: string, data: any): Promise<void> {
    try {
      const logDoc = doc(collection(db, this.CRON_LOG_COLLECTION))
      await setDoc(logDoc, {
        jobType,
        executedAt: serverTimestamp(),
        ...data,
      })
    } catch (error) {
      console.error("Error logging cron execution:", error)
    }
  }
}
