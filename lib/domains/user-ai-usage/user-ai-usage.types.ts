import { Timestamp } from "firebase/firestore"
import { BaseEntity } from "../base/base.types"

/**
 * AI usage categories
 */
export enum AIUsageCategory {
  TRIP = "trip",
  TASK = "task",
  ITINERARY = "itinerary",
  NEW_USER_ONBOARDING = "new_user_onboarding",
}

/**
 * Category usage period tracking
 */
export interface CategoryUsagePeriod {
  count: number
  lastReset: Timestamp | null
}

/**
 * Category usage tracking
 */
export interface CategoryUsageTracking {
  count: number
  lastReset: Timestamp | null
  daily?: CategoryUsagePeriod
  weekly?: CategoryUsagePeriod
  monthly?: CategoryUsagePeriod
}

/**
 * User AI usage entity
 */
export interface UserAIUsage extends BaseEntity {
  userId: string

  // Legacy fields (kept for backward compatibility)
  aiUsageToday: number
  aiUsageThisWeek: number
  aiUsageLastReset: Timestamp | null
  aiUsageWeekStart: Timestamp | null

  // Total usage across all categories
  totalUsage?: {
    count: number
    lastReset: Timestamp | null
    daily?: CategoryUsagePeriod
    weekly?: CategoryUsagePeriod
    monthly?: CategoryUsagePeriod
  }

  // Category-specific usage tracking
  categoryUsage?: {
    [AIUsageCategory.TRIP]?: CategoryUsageTracking
    [AIUsageCategory.TASK]?: CategoryUsageTracking
    [AIUsageCategory.ITINERARY]?: CategoryUsageTracking
    [AIUsageCategory.NEW_USER_ONBOARDING]?: CategoryUsageTracking
  }
}

/**
 * AI usage limits
 */
export const AI_USAGE_LIMITS = {
  FREE: {
    // Legacy limits
    DAILY: 10,
    WEEKLY: 50,

    // Category-specific limits
    CATEGORIES: {
      [AIUsageCategory.TRIP]: 3,
      [AIUsageCategory.TASK]: 3,
      [AIUsageCategory.ITINERARY]: 3,
      [AIUsageCategory.NEW_USER_ONBOARDING]: Infinity, // No limits for new user onboarding
    },
  },
  PRO: {
    // Legacy limits
    DAILY: Infinity,
    WEEKLY: Infinity,

    // Category-specific limits
    CATEGORIES: {
      [AIUsageCategory.TRIP]: Infinity,
      [AIUsageCategory.TASK]: Infinity,
      [AIUsageCategory.ITINERARY]: Infinity,
      [AIUsageCategory.NEW_USER_ONBOARDING]: Infinity, // No limits for new user onboarding
    },
  },
}

/**
 * Category usage data
 */
export interface CategoryUsageData {
  count: number
  limit: number
  canMakeRequest: boolean
}

/**
 * Period usage data
 */
export interface PeriodUsageData {
  count: number
  limit: number
}

/**
 * Total usage data
 */
export interface TotalUsageData {
  count: number
  daily?: PeriodUsageData
  weekly?: PeriodUsageData
  monthly?: PeriodUsageData
}

/**
 * Enhanced category usage data
 */
export interface EnhancedCategoryUsageData extends CategoryUsageData {
  daily?: PeriodUsageData
  weekly?: PeriodUsageData
  monthly?: PeriodUsageData
}

/**
 * AI usage summary
 */
export interface AIUsageSummary {
  // Legacy fields
  daily: number
  weekly: number
  dailyLimit: number
  weeklyLimit: number
  canMakeRequest: boolean

  // Total usage across all categories
  totalUsage?: TotalUsageData

  // New category-specific fields
  categories?: {
    [AIUsageCategory.TRIP]?: EnhancedCategoryUsageData
    [AIUsageCategory.TASK]?: EnhancedCategoryUsageData
    [AIUsageCategory.ITINERARY]?: EnhancedCategoryUsageData
    [AIUsageCategory.NEW_USER_ONBOARDING]?: EnhancedCategoryUsageData
  }
}
