import { BaseRealtimeService } from "../base/base.realtime.service"
import {
  AIUsageSummary,
  AI_USAGE_LIMITS,
  AIUsageCategory,
  UserAIUsage,
} from "./user-ai-usage.types"

/**
 * User AI usage real-time service for Firebase real-time operations
 */
export class UserAIUsageRealtimeService {
  private static readonly COLLECTION = "userAiUsage"

  /**
   * Subscribe to AI usage by user ID
   * @param userId User ID
   * @param hasSubscription Whether the user has an active subscription
   * @param callback Callback function to handle usage changes
   * @returns Unsubscribe function
   */
  static subscribeToUserAIUsage(
    userId: string,
    hasSubscription: boolean,
    callback: (usage: AIUsageSummary | null, error?: Error) => void
  ): () => void {
    return BaseRealtimeService.subscribeToDocument<UserAIUsage>(
      this.COLLECTION,
      userId,
      async (userAIUsageData, error) => {
        if (error) {
          callback(null, error)
          return
        }

        if (!userAIUsageData) {
          callback(null)
          return
        }

        try {
          // Determine limits based on subscription status
          const dailyLimit = hasSubscription
            ? AI_USAGE_LIMITS.PRO.DAILY
            : AI_USAGE_LIMITS.FREE.DAILY
          const weeklyLimit = hasSubscription
            ? AI_USAGE_LIMITS.PRO.WEEKLY
            : AI_USAGE_LIMITS.FREE.WEEKLY

          // Get category limits
          const categoryLimits = hasSubscription
            ? AI_USAGE_LIMITS.PRO.CATEGORIES
            : AI_USAGE_LIMITS.FREE.CATEGORIES

          // Check daily counter
          const now = new Date()
          const today = new Date(now.getFullYear(), now.getMonth(), now.getDate()).getTime()

          let daily = userAIUsageData.aiUsageToday || 0

          // Reset daily counter if it's a new day
          if (
            !userAIUsageData.aiUsageLastReset ||
            (userAIUsageData.aiUsageLastReset.toMillis &&
              userAIUsageData.aiUsageLastReset.toMillis() < today)
          ) {
            daily = 0
            // REMOVED: Database update in listener to prevent infinite loops
            // The reset will be handled by the service when incrementing usage
            // await updateDoc(doc(db, this.COLLECTION, userId), { ... })
          }

          // Check weekly counter
          const oneWeekAgo = new Date()
          oneWeekAgo.setDate(oneWeekAgo.getDate() - 7)
          const weekStart = oneWeekAgo.getTime()

          let weekly = userAIUsageData.aiUsageThisWeek || 0

          // Reset weekly counter if it's been more than a week
          if (
            !userAIUsageData.aiUsageWeekStart ||
            (userAIUsageData.aiUsageWeekStart.toMillis &&
              userAIUsageData.aiUsageWeekStart.toMillis() < weekStart)
          ) {
            weekly = 0
            // REMOVED: Database update in listener to prevent infinite loops
            // The reset will be handled by the service when incrementing usage
            // await updateDoc(doc(db, this.COLLECTION, userId), { ... })
          }

          // Determine if the user can make a request (legacy check)
          const canMakeRequest = hasSubscription || (daily < dailyLimit && weekly < weeklyLimit)

          // Process category-specific usage data
          const categories: AIUsageSummary["categories"] = {}

          // Initialize with default values for all categories
          Object.values(AIUsageCategory).forEach((category) => {
            categories[category] = {
              count: 0,
              limit: categoryLimits[category],
              canMakeRequest: hasSubscription || true, // Pro users can always make requests
            }
          })

          // Update with actual values if they exist
          if (userAIUsageData.categoryUsage) {
            Object.entries(userAIUsageData.categoryUsage).forEach(([category, data]) => {
              if (Object.values(AIUsageCategory).includes(category as AIUsageCategory)) {
                const typedCategory = category as AIUsageCategory
                const count = data.count || 0
                const limit = categoryLimits[typedCategory]

                categories[typedCategory] = {
                  count,
                  limit,
                  canMakeRequest: hasSubscription || count < limit,
                }
              }
            })
          }

          callback({
            daily,
            weekly,
            dailyLimit,
            weeklyLimit,
            canMakeRequest,
            categories,
          })
        } catch (err) {
          console.error("Error processing AI usage data:", err)
          callback(null, err as Error)
        }
      }
    )
  }
}
