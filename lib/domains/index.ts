// Base
export * from "./base/base.types"
export * from "./base/base.service"
export * from "./base/base.realtime.service"
export * from "./base/base.realtime.hooks"

// Auth
export * from "./auth/auth.types"
export * from "./auth/auth.service"
export * from "./auth/auth.store"
export * from "./auth/auth.hooks"

// User
export * from "./user/user.types"
export * from "./user/user.service"
export * from "./user/user.store"
export * from "./user/user.hooks"

// User Preferences
export * from "./user-preferences/user-preferences.types"
export * from "./user-preferences/user-preferences.service"
export * from "./user-preferences/user-preferences.store"
export * from "./user-preferences/user-preferences.hooks"

// Activity Preferences
export * from "./activity-preferences/activity-preferences.types"
export * from "./activity-preferences/activity-preferences.service"
export * from "./activity-preferences/activity-preferences.store"
export * from "./activity-preferences/activity-preferences.hooks"

// Google Places
export * from "./google-places/google-places.hooks"

// User AI Usage
export * from "./user-ai-usage/user-ai-usage.types"
export * from "./user-ai-usage/user-ai-usage.service"
export * from "./user-ai-usage/user-ai-usage.store"
export * from "./user-ai-usage/user-ai-usage.hooks"

// User Subscription (prioritize flat architecture)
export * from "./user-subscription/user-subscription.types"
export * from "./user-subscription/flat-subscription.service"
export * from "./user-subscription/subscription-aggregation.service"
export * from "./user-subscription/perk-aware-subscription.service"
// Legacy exports (deprecated)
export * from "./user-subscription/user-subscription.service"
export * from "./user-subscription/user-subscription.store"
export * from "./user-subscription/user-subscription.hooks"

// Referral
export * from "./referral/referral.types"
export * from "./referral/referral.service"
export * from "./referral/referral.store"
export * from "./referral/referral.hooks"
export * from "./referral/referral.realtime.service"
export * from "./referral/referral.realtime.hooks"

// Perk
export * from "./perk/perk.types"
export * from "./perk/perk.service"
export * from "./perk/perk.store"
export * from "./perk/perk.hooks"
export * from "./perk/perk.realtime.service"
export * from "./perk/perk.realtime.hooks"

// Travel Details
export * from "./travel-details/travel-details.types"
export * from "./travel-details/travel-details.service"
export * from "./travel-details/travel-details.store"
export * from "./travel-details/travel-details.hooks"
export * from "./travel-details/travel-details.realtime.service"
export * from "./travel-details/travel-details.realtime.hooks"
export * from "./user-ai-usage/user-ai-usage.realtime.service"
export * from "./user-ai-usage/user-ai-usage.realtime.hooks"

// Message
export * from "./message/message.types"
export * from "./message/message.service"
export * from "./message/message.store"
export * from "./message/message.hooks"
export * from "./message/message.realtime.service"
export * from "./message/message.realtime.hooks"

// Notification
export * from "./notification/notification.types"
export * from "./notification/notification.service"
export * from "./notification/notification.store"
export * from "./notification/notification.hooks"
export * from "./notification/notification.realtime.service"
export * from "./notification/notification.realtime.hooks"

// Trip
export * from "./trip/trip.types"
export * from "./trip/trip.service"
export * from "./trip/trip.store"
export * from "./trip/trip.hooks"

// Trip Review
export * from "./trip-review/trip-review.types"
export * from "./trip-review/trip-review.service"
export * from "./trip-review/trip-review.store"
export * from "./trip-review/trip-review.hooks"
export * from "./trip-review/trip-review.realtime.service"
export * from "./trip-review/trip-review.realtime.hooks"

// Squad
export * from "./squad/squad.types"
export * from "./squad/squad.service"
export * from "./squad/squad.store"
export * from "./squad/squad.hooks"

// Task
export * from "./task/task.types"
export * from "./task/task.service"
export * from "./task/task.store"
export * from "./task/task.hooks"

// Invitation
export * from "./invitation/invitation.types"
export * from "./invitation/invitation.service"
export * from "./invitation/invitation.store"
export * from "./invitation/invitation.hooks"

// User-Trip
export * from "./user-trip/user-trip.types"
export * from "./user-trip/user-trip.service"
export * from "./user-trip/user-trip.store"
export * from "./user-trip/user-trip.hooks"

// Trip-Savings
export * from "./trip-savings/trip-savings.types"
export * from "./trip-savings/trip-savings.service"
export * from "./trip-savings/trip-savings.store"
export * from "./trip-savings/trip-savings.hooks"

// Itinerary
export * from "./itinerary/itinerary.types"
export * from "./itinerary/itinerary.service"
export * from "./itinerary/itinerary.store"
export * from "./itinerary/itinerary.hooks"
