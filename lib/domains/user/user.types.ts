import { Timestamp } from "firebase/firestore"
import { BaseEntity } from "../base/base.types"

/**
 * User entity
 */
export interface User extends BaseEntity {
  uid: string
  email: string
  displayName: string
  photoURL?: string
  bio?: string
  isAdmin?: boolean
  newUser?: boolean
  firstLogin?: Timestamp
}

/**
 * User creation data
 */
export type UserCreateData = Omit<User, "id" | "createdAt">

/**
 * User update data
 */
export type UserUpdateData = Partial<User>
