import { getAdminInstance } from "@/lib/firebase-admin"
import {
  ReferralCode,
  ReferralCodeCreateData,
  ReferralValidationResult,
  ReferralProcessingResult,
  UserReferralCreateData,
} from "./referral.types"

/**
 * Admin Referral Service for server-side operations
 * Uses Firebase Admin SDK for operations that don't require user authentication
 */
export class ReferralAdminService {
  private static readonly REFERRAL_COLLECTION = "referral"
  private static readonly USER_REFERRALS_SUBCOLLECTION = "referrals"

  /**
   * Generate a unique 8-character alphanumeric referral code
   */
  static async generateReferralCode(length = 8, maxRetries = 10): Promise<string> {
    const { adminDb } = await getAdminInstance()
    if (!adminDb) {
      throw new Error("Firebase Admin is not initialized")
    }

    const characters = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"

    for (let attempt = 0; attempt < maxRetries; attempt++) {
      let code = ""
      for (let i = 0; i < length; i++) {
        code += characters.charAt(Math.floor(Math.random() * characters.length))
      }

      // Check if code already exists
      const existingDoc = await adminDb.collection(this.REFERRAL_COLLECTION).doc(code).get()
      if (!existingDoc.exists) {
        return code
      }
    }

    throw new Error(`Failed to generate unique referral code after ${maxRetries} attempts`)
  }

  /**
   * Create a referral code for a user
   */
  static async createReferralCode(code: string, userId: string): Promise<void> {
    const { adminDb, adminFieldValue } = await getAdminInstance()
    if (!adminDb || !adminFieldValue) {
      throw new Error("Firebase Admin is not initialized")
    }

    const referralData: ReferralCodeCreateData = {
      userId,
      totalReferrals: 0,
      isActive: true,
    }

    await adminDb.collection(this.REFERRAL_COLLECTION).doc(code).set({
      ...referralData,
      createdAt: adminFieldValue.serverTimestamp(),
      updatedAt: adminFieldValue.serverTimestamp(),
    })
  }

  /**
   * Get a referral code by ID
   */
  static async getReferralCode(code: string): Promise<ReferralCode | null> {
    const { adminDb } = await getAdminInstance()
    if (!adminDb) {
      throw new Error("Firebase Admin is not initialized")
    }

    const doc = await adminDb.collection(this.REFERRAL_COLLECTION).doc(code).get()
    if (!doc.exists) {
      return null
    }

    return {
      id: doc.id,
      ...doc.data(),
    } as ReferralCode
  }

  /**
   * Validate a referral code
   */
  static async validateReferralCode(code: string): Promise<ReferralValidationResult> {
    try {
      console.log("🔍 [Admin] Validating referral code:", code)
      const referralCode = await this.getReferralCode(code)
      console.log("📋 [Admin] Retrieved referral code data:", referralCode)

      if (!referralCode) {
        console.log("❌ [Admin] Referral code does not exist")
        return {
          isValid: false,
          exists: false,
          isActive: false,
          error: "Referral code does not exist",
        }
      }

      if (!referralCode.isActive) {
        console.log("❌ [Admin] Referral code is not active")
        return {
          isValid: false,
          exists: true,
          isActive: false,
          ownerId: referralCode.userId,
          error: "Referral code is not active",
        }
      }

      console.log("✅ [Admin] Referral code is valid")
      return {
        isValid: true,
        exists: true,
        isActive: true,
        ownerId: referralCode.userId,
      }
    } catch (error) {
      console.error("💥 [Admin] Error validating referral code:", error)
      return {
        isValid: false,
        exists: false,
        isActive: false,
        error: "Error validating referral code",
      }
    }
  }

  /**
   * Process a referral (increment count and create referral record)
   */
  static async processReferral(
    referralCode: string,
    referredUserId: string,
    referredUserEmail: string
  ): Promise<ReferralProcessingResult> {
    try {
      console.log("🔍 [Admin] Starting referral processing:", {
        referralCode,
        referredUserId,
        referredUserEmail,
      })

      // Validate the referral code first
      const validation = await this.validateReferralCode(referralCode)
      console.log("✅ [Admin] Referral validation result:", validation)

      if (!validation.isValid || !validation.ownerId) {
        console.log("❌ [Admin] Referral validation failed")
        return {
          success: false,
          error: validation.error || "Invalid referral code",
        }
      }

      const referrerId = validation.ownerId
      console.log("👤 [Admin] Referrer ID:", referrerId)

      // Prevent self-referral
      if (referrerId === referredUserId) {
        console.log("🚫 [Admin] Self-referral attempt blocked")
        return {
          success: false,
          error: "Cannot refer yourself",
        }
      }

      const { adminDb, adminFieldValue } = await getAdminInstance()
      if (!adminDb || !adminFieldValue) {
        throw new Error("Firebase Admin is not initialized")
      }

      // Use a transaction to ensure consistency
      const result = await adminDb.runTransaction(async (transaction) => {
        // Get current referral code data
        const referralCodeRef = adminDb.collection(this.REFERRAL_COLLECTION).doc(referralCode)
        const referralCodeDoc = await transaction.get(referralCodeRef)

        if (!referralCodeDoc.exists) {
          throw new Error("Referral code not found")
        }

        const currentData = referralCodeDoc.data() as ReferralCode
        console.log("📊 [Admin] Current referral data:", currentData)
        const newTotalReferrals = currentData.totalReferrals + 1
        console.log(
          "📈 [Admin] Incrementing totalReferrals from",
          currentData.totalReferrals,
          "to",
          newTotalReferrals
        )

        // Update referral code with incremented count
        transaction.update(referralCodeRef, {
          totalReferrals: newTotalReferrals,
          updatedAt: adminFieldValue.serverTimestamp(),
        })
        console.log("💾 [Admin] Updated referral code document with new count")

        // Create referral record for the referrer
        const userReferralRef = adminDb
          .collection("users")
          .doc(referrerId)
          .collection(this.USER_REFERRALS_SUBCOLLECTION)
          .doc()

        console.log(
          "📝 [Admin] Creating referral record at:",
          `users/${referrerId}/referrals/${userReferralRef.id}`
        )

        const referralData: UserReferralCreateData = {
          referredUserId,
          referredUserEmail,
          referralCode,
          status: "completed",
        }
        console.log("📋 [Admin] Referral data to save:", referralData)

        transaction.set(userReferralRef, {
          ...referralData,
          createdAt: adminFieldValue.serverTimestamp(),
          updatedAt: adminFieldValue.serverTimestamp(),
        })
        console.log("💾 [Admin] Referral record created for referrer")

        return {
          referralId: userReferralRef.id,
          newTotalReferrals,
        }
      })

      // Check for newly unlocked perks based on newTotalReferrals
      let perksUnlocked: string[] = []
      try {
        const { PerkAdminService } = await import("../perk/perk.admin.service")
        perksUnlocked = await PerkAdminService.unlockEligiblePerks(referrerId, result.newTotalReferrals)
      } catch (error) {
        console.error("Error checking for unlocked perks:", error)
        // Don't fail the referral process if perk checking fails
      }

      console.log("✅ [Admin] Referral processing completed successfully")
      return {
        success: true,
        referralId: result.referralId,
        perksUnlocked,
      }
    } catch (error) {
      console.error("💥 [Admin] Error processing referral:", error)
      return {
        success: false,
        error: error instanceof Error ? error.message : "Unknown error",
      }
    }
  }
}
