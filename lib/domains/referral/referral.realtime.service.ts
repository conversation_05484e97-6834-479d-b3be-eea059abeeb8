import { db } from "@/lib/firebase"
import {
  collection,
  doc,
  onSnapshot,
  query,
  where,
  orderBy,
  Unsubscribe,
} from "firebase/firestore"
import { BaseRealtimeService } from "../base/base.realtime.service"
import { ReferralCode, UserReferral } from "./referral.types"

/**
 * Referral realtime service for Firebase operations
 */
export class ReferralRealtimeService extends BaseRealtimeService {
  private static readonly REFERRAL_COLLECTION = "referral"
  private static readonly USER_REFERRALS_SUBCOLLECTION = "referrals"

  /**
   * Subscribe to user's referral code changes
   */
  static subscribeToUserReferralCode(
    userId: string,
    callback: (referralCode: ReferralCode | null) => void,
    onError?: (error: Error) => void
  ): Unsubscribe {
    try {
      const q = query(
        collection(db, this.REFERRAL_COLLECTION),
        where("userId", "==", userId)
      )

      return onSnapshot(
        q,
        (snapshot) => {
          try {
            if (snapshot.empty) {
              callback(null)
              return
            }

            const doc = snapshot.docs[0]
            const referralCode: ReferralCode = {
              id: doc.id,
              ...doc.data(),
            } as ReferralCode

            callback(referralCode)
          } catch (error) {
            console.error("Error processing referral code snapshot:", error)
            onError?.(error as Error)
          }
        },
        (error) => {
          console.error("Error in referral code subscription:", error)
          onError?.(error)
        }
      )
    } catch (error) {
      console.error("Error setting up referral code subscription:", error)
      onError?.(error as Error)
      return () => {} // Return empty unsubscribe function
    }
  }

  /**
   * Subscribe to user's referrals changes
   */
  static subscribeToUserReferrals(
    userId: string,
    callback: (referrals: UserReferral[]) => void,
    onError?: (error: Error) => void
  ): Unsubscribe {
    try {
      const referralsRef = collection(
        db,
        "users",
        userId,
        this.USER_REFERRALS_SUBCOLLECTION
      )
      
      const q = query(referralsRef, orderBy("createdAt", "desc"))

      return onSnapshot(
        q,
        (snapshot) => {
          try {
            const referrals: UserReferral[] = snapshot.docs.map((doc) => ({
              id: doc.id,
              ...doc.data(),
            } as UserReferral))

            callback(referrals)
          } catch (error) {
            console.error("Error processing user referrals snapshot:", error)
            onError?.(error as Error)
          }
        },
        (error) => {
          console.error("Error in user referrals subscription:", error)
          onError?.(error)
        }
      )
    } catch (error) {
      console.error("Error setting up user referrals subscription:", error)
      onError?.(error as Error)
      return () => {} // Return empty unsubscribe function
    }
  }

  /**
   * Subscribe to a specific referral code
   */
  static subscribeToReferralCode(
    code: string,
    callback: (referralCode: ReferralCode | null) => void,
    onError?: (error: Error) => void
  ): Unsubscribe {
    try {
      const docRef = doc(db, this.REFERRAL_COLLECTION, code)

      return onSnapshot(
        docRef,
        (snapshot) => {
          try {
            if (snapshot.exists()) {
              const referralCode: ReferralCode = {
                id: snapshot.id,
                ...snapshot.data(),
              } as ReferralCode

              callback(referralCode)
            } else {
              callback(null)
            }
          } catch (error) {
            console.error("Error processing referral code snapshot:", error)
            onError?.(error as Error)
          }
        },
        (error) => {
          console.error("Error in referral code subscription:", error)
          onError?.(error)
        }
      )
    } catch (error) {
      console.error("Error setting up referral code subscription:", error)
      onError?.(error as Error)
      return () => {} // Return empty unsubscribe function
    }
  }
}
