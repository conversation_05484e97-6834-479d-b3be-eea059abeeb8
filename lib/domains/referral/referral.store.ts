"use client"

import { create } from "zustand"
import { persist } from "zustand/middleware"
import { ReferralCode, UserReferral } from "./referral.types"
import { ReferralService } from "./referral.service"

/**
 * Referral store state interface
 */
interface ReferralState {
  // State
  userReferralCode: ReferralCode | null
  userReferrals: UserReferral[]
  loading: boolean
  error: Error | null

  // Actions
  setUserReferralCode: (referralCode: ReferralCode | null) => void
  setUserReferrals: (referrals: UserReferral[]) => void
  setLoading: (loading: boolean) => void
  setError: (error: Error | null) => void

  // Async actions
  fetchUserReferralCode: (userId: string) => Promise<void>
  fetchUserReferrals: (userId: string) => Promise<void>
  generateAndCreateReferralCode: (userId: string) => Promise<string | null>
  validateReferralCode: (code: string) => Promise<boolean>
  processReferral: (
    referralCode: string,
    referredUserId: string,
    referredUserEmail: string
  ) => Promise<boolean>

  // Computed values
  getTotalReferrals: () => number
  getReferralProgress: (targetCount: number) => { current: number; target: number; percentage: number }
}

/**
 * Referral store with Zustand
 */
export const useReferralStore = create<ReferralState>()(
  persist(
    (set, get) => ({
      // Initial state
      userReferralCode: null,
      userReferrals: [],
      loading: false,
      error: null,

      // Actions
      setUserReferralCode: (referralCode) => set({ userReferralCode: referralCode }),
      setUserReferrals: (referrals) => set({ userReferrals: referrals }),
      setLoading: (loading) => set({ loading }),
      setError: (error) => set({ error }),

      // Async actions
      fetchUserReferralCode: async (userId: string) => {
        try {
          set({ loading: true, error: null })
          const referralCode = await ReferralService.getReferralCodeByUserId(userId)
          set({ userReferralCode: referralCode })
        } catch (error) {
          console.error("Error fetching user referral code:", error)
          set({ error: error as Error })
        } finally {
          set({ loading: false })
        }
      },

      fetchUserReferrals: async (userId: string) => {
        try {
          set({ loading: true, error: null })
          const referrals = await ReferralService.getUserReferrals(userId)
          set({ userReferrals: referrals })
        } catch (error) {
          console.error("Error fetching user referrals:", error)
          set({ error: error as Error })
        } finally {
          set({ loading: false })
        }
      },

      generateAndCreateReferralCode: async (userId: string) => {
        try {
          set({ loading: true, error: null })
          
          // Check if user already has a referral code
          const existingCode = await ReferralService.getReferralCodeByUserId(userId)
          if (existingCode) {
            set({ userReferralCode: existingCode })
            return existingCode.id
          }

          // Generate new code
          const code = await ReferralService.generateReferralCode()
          const result = await ReferralService.createReferralCode(code, userId)

          if (result.success && result.data) {
            set({ userReferralCode: result.data })
            return code
          } else {
            throw new Error("Failed to create referral code")
          }
        } catch (error) {
          console.error("Error generating referral code:", error)
          set({ error: error as Error })
          return null
        } finally {
          set({ loading: false })
        }
      },

      validateReferralCode: async (code: string) => {
        try {
          const validation = await ReferralService.validateReferralCode(code)
          return validation.isValid
        } catch (error) {
          console.error("Error validating referral code:", error)
          return false
        }
      },

      processReferral: async (
        referralCode: string,
        referredUserId: string,
        referredUserEmail: string
      ) => {
        try {
          set({ loading: true, error: null })
          const result = await ReferralService.processReferral(
            referralCode,
            referredUserId,
            referredUserEmail
          )

          if (result.success) {
            // Refresh the referrer's data if this is their referral code
            const { userReferralCode } = get()
            if (userReferralCode?.id === referralCode) {
              // Refresh referral code data to get updated count
              const updatedCode = await ReferralService.getReferralCode(referralCode)
              if (updatedCode) {
                set({ userReferralCode: updatedCode })
              }
              
              // Refresh referrals list
              const referrals = await ReferralService.getUserReferrals(userReferralCode.userId)
              set({ userReferrals: referrals })
            }
          }

          return result.success
        } catch (error) {
          console.error("Error processing referral:", error)
          set({ error: error as Error })
          return false
        } finally {
          set({ loading: false })
        }
      },

      // Computed values
      getTotalReferrals: () => {
        const { userReferralCode } = get()
        return userReferralCode?.totalReferrals || 0
      },

      getReferralProgress: (targetCount: number) => {
        const current = get().getTotalReferrals()
        return {
          current,
          target: targetCount,
          percentage: Math.min((current / targetCount) * 100, 100),
        }
      },
    }),
    {
      name: "referral-store",
      partialize: (state) => ({
        userReferralCode: state.userReferralCode,
        userReferrals: state.userReferrals,
      }),
    }
  )
)
