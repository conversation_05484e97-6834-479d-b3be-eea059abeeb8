"use client"

import { create } from "zustand"
import { Notification, NotificationCreateData } from "./notification.types"
import { NotificationService } from "./notification.service"
import { toast } from "@/components/ui/use-toast"

/**
 * Notification store state interface
 */
interface NotificationState {
  // State
  notifications: Notification[]
  unreadCount: number
  loading: boolean
  error: Error | null
  markingAsRead: Record<string, boolean> // Keyed by notification ID

  // Actions
  setNotifications: (notifications: Notification[]) => void
  addNotification: (notification: Notification) => void
  setUnreadCount: (count: number) => void
  setLoading: (loading: boolean) => void
  setError: (error: Error | null) => void
  setMarkingAsRead: (notificationId: string, marking: boolean) => void
  markAsRead: (notificationId: string) => Promise<void>
  markAllAsRead: () => Promise<void>
  deleteNotification: (notificationId: string) => Promise<void>
  createMessageMentionNotifications: (
    mentionedUserIds: string[],
    notificationData: Omit<NotificationCreateData, "userId">
  ) => Promise<void>
}

/**
 * Notification store
 */
export const useNotificationStore = create<NotificationState>((set, get) => ({
  // Initial state
  notifications: [],
  unreadCount: 0,
  loading: false,
  error: null,
  markingAsRead: {},

  // Actions
  setNotifications: (notifications) =>
    set(() => ({
      notifications,
    })),

  addNotification: (notification) =>
    set((state) => ({
      notifications: [notification, ...state.notifications],
    })),

  setUnreadCount: (count) =>
    set(() => ({
      unreadCount: count,
    })),

  setLoading: (loading) =>
    set(() => ({
      loading,
    })),

  setError: (error) =>
    set(() => ({
      error,
    })),

  setMarkingAsRead: (notificationId, marking) =>
    set((state) => ({
      markingAsRead: { ...state.markingAsRead, [notificationId]: marking },
    })),

  markAsRead: async (notificationId) => {
    const { setMarkingAsRead, setError, notifications, setNotifications } = get()

    try {
      setMarkingAsRead(notificationId, true)
      setError(null)

      // Get current user ID from the notification
      const notification = notifications.find((n) => n.id === notificationId)
      if (!notification) {
        throw new Error("Notification not found")
      }

      const response = await NotificationService.markAsRead(notification.userId, notificationId)

      if (!response.success) {
        throw response.error || new Error("Failed to mark notification as read")
      }

      // Optimistically update the notification in the store
      const updatedNotifications = notifications.map((n) =>
        n.id === notificationId ? { ...n, read: true } : n
      )
      setNotifications(updatedNotifications)
    } catch (error) {
      console.error("Error marking notification as read:", error)
      setError(error as Error)
      toast({
        title: "Error",
        description: "Failed to mark notification as read. Please try again.",
        variant: "destructive",
      })
    } finally {
      setMarkingAsRead(notificationId, false)
    }
  },

  markAllAsRead: async () => {
    const { setLoading, setError, notifications, setNotifications } = get()

    try {
      setLoading(true)
      setError(null)

      // Get current user ID from the first notification
      const firstNotification = notifications[0]
      if (!firstNotification) {
        return // No notifications to mark as read
      }

      const response = await NotificationService.markAllAsRead(firstNotification.userId)

      if (!response.success) {
        throw response.error || new Error("Failed to mark all notifications as read")
      }

      // Optimistically update all notifications in the store
      const updatedNotifications = notifications.map((n) => ({ ...n, read: true }))
      setNotifications(updatedNotifications)

      toast({
        title: "Success",
        description: "All notifications marked as read.",
      })
    } catch (error) {
      console.error("Error marking all notifications as read:", error)
      setError(error as Error)
      toast({
        title: "Error",
        description: "Failed to mark all notifications as read. Please try again.",
        variant: "destructive",
      })
    } finally {
      setLoading(false)
    }
  },

  deleteNotification: async (notificationId) => {
    const { setError, notifications, setNotifications } = get()

    try {
      setError(null)

      // Get current user ID from the notification
      const notification = notifications.find((n) => n.id === notificationId)
      if (!notification) {
        throw new Error("Notification not found")
      }

      const response = await NotificationService.deleteNotification(
        notification.userId,
        notificationId
      )

      if (!response.success) {
        throw response.error || new Error("Failed to delete notification")
      }

      // Remove the notification from the store
      const updatedNotifications = notifications.filter((n) => n.id !== notificationId)
      setNotifications(updatedNotifications)
    } catch (error) {
      console.error("Error deleting notification:", error)
      setError(error as Error)
      toast({
        title: "Error",
        description: "Failed to delete notification. Please try again.",
        variant: "destructive",
      })
    }
  },

  createMessageMentionNotifications: async (mentionedUserIds, notificationData) => {
    const { setError } = get()

    try {
      setError(null)

      await NotificationService.createMessageMentionNotifications(
        mentionedUserIds,
        notificationData
      )

      // Notifications will be added via real-time subscription
    } catch (error) {
      console.error("Error creating message mention notifications:", error)
      setError(error as Error)
      // Don't show toast for this error as it's background operation
    }
  },
}))

/**
 * Selectors for notification store
 */
export const useNotificationSelectors = {
  notifications: () => useNotificationStore((state) => state.notifications),
  unreadCount: () => useNotificationStore((state) => state.unreadCount),
  loading: () => useNotificationStore((state) => state.loading),
  error: () => useNotificationStore((state) => state.error),
  markingAsRead: (notificationId: string) =>
    useNotificationStore((state) => state.markingAsRead[notificationId] ?? false),
}

/**
 * Individual selector hooks for better performance
 */
export const useNotificationsData = () => useNotificationStore((state) => state.notifications)

export const useUnreadCount = () => useNotificationStore((state) => state.unreadCount)

export const useNotificationLoading = () => useNotificationStore((state) => state.loading)

export const useNotificationError = () => useNotificationStore((state) => state.error)

export const useNotificationMarkingAsRead = (notificationId: string) =>
  useNotificationStore((state) => state.markingAsRead[notificationId] ?? false)
