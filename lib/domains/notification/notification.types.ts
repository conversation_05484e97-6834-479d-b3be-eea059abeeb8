import { Timestamp } from "firebase/firestore"
import { BaseEntity } from "../base/base.types"

/**
 * Notification type
 */
export type NotificationType =
  | "message_mention"
  | "trip_update"
  | "task_assigned"
  | "invitation"
  | "trip_completed"

/**
 * Notification entity
 */
export interface Notification extends BaseEntity {
  userId: string
  type: NotificationType
  title: string
  message: string
  read: boolean
  actionUrl: string
  relatedEntityId: string // tripId for message mentions, taskId for task assignments, etc.
  relatedEntityType?: string // "trip", "task", "squad", etc.
  senderUserId?: string // ID of the user who triggered the notification
  senderUserName?: string // Name of the user who triggered the notification
  senderUserPhotoURL?: string // Photo URL of the user who triggered the notification
}

/**
 * Notification creation data
 */
export type NotificationCreateData = Omit<Notification, "id" | "createdAt" | "updatedAt">

/**
 * Notification update data
 */
export type NotificationUpdateData = Partial<Pick<Notification, "read">>
