"use client"

import { useEffect } from "react"
import { useUser } from "../auth/auth.hooks"
import { usePerkStore } from "./perk.store"

/**
 * Basic perk store selectors
 */
export const useGlobalPerks = () => usePerkStore((state) => state.globalPerks)
export const useUserPerks = () => usePerkStore((state) => state.userPerks)
export const useUserPerkSummary = () => usePerkStore((state) => state.userPerkSummary)
export const usePerkLoading = () => usePerkStore((state) => state.loading)
export const usePerkError = () => usePerkStore((state) => state.error)

/**
 * Perk action selectors
 */
export const useFetchGlobalPerks = () => usePerkStore((state) => state.fetchGlobalPerks)
export const useFetchUserPerks = () => usePerkStore((state) => state.fetchUserPerks)
export const useFetchUserPerkSummary = () => usePerkStore((state) => state.fetchUserPerkSummary)
export const useUnlockEligiblePerks = () => usePerkStore((state) => state.unlockEligiblePerks)
export const useApplySubscriptionPerk = () => usePerkStore((state) => state.applySubscriptionPerk)

/**
 * Computed value selectors
 */
export const useGetUnlockedPerks = () => usePerkStore((state) => state.getUnlockedPerks)
export const useGetAppliedPerks = () => usePerkStore((state) => state.getAppliedPerks)
export const useGetExpiredPerks = () => usePerkStore((state) => state.getExpiredPerks)
export const useHasUnlockedPerk = () => usePerkStore((state) => state.hasUnlockedPerk)
export const useGetAdditionalSquadLimit = () => usePerkStore((state) => state.getAdditionalSquadLimit)
export const useGetAdditionalTripLimit = () => usePerkStore((state) => state.getAdditionalTripLimit)

/**
 * Hook to get global perks with auto-initialization
 */
export const useGlobalPerksWithInit = () => {
  const globalPerks = useGlobalPerks()
  const loading = usePerkLoading()
  const fetchGlobalPerks = useFetchGlobalPerks()

  // Fetch global perks on mount if not already loaded
  useEffect(() => {
    if (globalPerks.length === 0 && !loading) {
      fetchGlobalPerks()
    }
  }, [globalPerks.length, loading, fetchGlobalPerks])

  return {
    globalPerks,
    loading,
  }
}

/**
 * Hook to get user perks with auto-initialization
 */
export const useUserPerksWithInit = () => {
  const user = useUser()
  const userPerks = useUserPerks()
  const loading = usePerkLoading()
  const fetchUserPerks = useFetchUserPerks()

  // Fetch user perks when user changes
  useEffect(() => {
    if (user?.uid) {
      fetchUserPerks(user.uid)
    }
  }, [user, fetchUserPerks])

  return {
    userPerks,
    loading,
  }
}

/**
 * Hook to get user perk summary with auto-initialization
 */
export const useUserPerkSummaryWithInit = () => {
  const user = useUser()
  const userPerkSummary = useUserPerkSummary()
  const loading = usePerkLoading()
  const fetchUserPerkSummary = useFetchUserPerkSummary()

  // Fetch user perk summary when user changes
  useEffect(() => {
    if (user?.uid) {
      fetchUserPerkSummary(user.uid)
    }
  }, [user, fetchUserPerkSummary])

  return {
    userPerkSummary,
    loading,
  }
}

/**
 * Combined hook for all perk data with auto-initialization
 */
export const usePerksWithInit = () => {
  const user = useUser()
  const { globalPerks } = useGlobalPerksWithInit()
  const { userPerks } = useUserPerksWithInit()
  const { userPerkSummary } = useUserPerkSummaryWithInit()
  const loading = usePerkLoading()
  const error = usePerkError()

  // Computed values
  const getUnlockedPerks = useGetUnlockedPerks()
  const getAppliedPerks = useGetAppliedPerks()
  const getExpiredPerks = useGetExpiredPerks()
  const hasUnlockedPerk = useHasUnlockedPerk()
  const getAdditionalSquadLimit = useGetAdditionalSquadLimit()
  const getAdditionalTripLimit = useGetAdditionalTripLimit()

  const unlockedPerks = getUnlockedPerks()
  const appliedPerks = getAppliedPerks()
  const expiredPerks = getExpiredPerks()
  const additionalSquadLimit = getAdditionalSquadLimit()
  const additionalTripLimit = getAdditionalTripLimit()

  return {
    user,
    globalPerks,
    userPerks,
    userPerkSummary,
    loading,
    error,
    unlockedPerks,
    appliedPerks,
    expiredPerks,
    additionalSquadLimit,
    additionalTripLimit,
    hasUnlockedPerk,
  }
}

/**
 * Hook for perk-aware limit calculations
 */
export const usePerkAwareLimits = () => {
  const { userPerkSummary } = useUserPerkSummaryWithInit()

  const getEnhancedSquadLimit = (baseLimit: number) => {
    const additionalSquads = userPerkSummary?.additionalSquads || 0
    return baseLimit + additionalSquads
  }

  const getEnhancedTripLimit = (baseLimit: number) => {
    const additionalTrips = userPerkSummary?.additionalTrips || 0
    return baseLimit + additionalTrips
  }

  const getEnhancedDailyAILimit = (baseLimit: number) => {
    const additionalDaily = userPerkSummary?.additionalDailyAI || 0
    return baseLimit + additionalDaily
  }

  const getEnhancedWeeklyAILimit = (baseLimit: number) => {
    const additionalWeekly = userPerkSummary?.additionalWeeklyAI || 0
    return baseLimit + additionalWeekly
  }

  return {
    userPerkSummary,
    getEnhancedSquadLimit,
    getEnhancedTripLimit,
    getEnhancedDailyAILimit,
    getEnhancedWeeklyAILimit,
  }
}

/**
 * Hook for handling perk unlocking after referrals
 */
export const usePerkUnlocking = () => {
  const user = useUser()
  const unlockEligiblePerks = useUnlockEligiblePerks()
  const applySubscriptionPerk = useApplySubscriptionPerk()

  const handleReferralPerkUnlocking = async (referralCount: number) => {
    if (!user?.uid) return []

    try {
      const unlockedPerkIds = await unlockEligiblePerks(user.uid, referralCount)
      return unlockedPerkIds
    } catch (error) {
      console.error("Error handling referral perk unlocking:", error)
      return []
    }
  }

  const handleSubscriptionPerkApplication = async (perkId: string) => {
    if (!user?.uid) return false

    try {
      return await applySubscriptionPerk(user.uid, perkId)
    } catch (error) {
      console.error("Error applying subscription perk:", error)
      return false
    }
  }

  return {
    handleReferralPerkUnlocking,
    handleSubscriptionPerkApplication,
  }
}
