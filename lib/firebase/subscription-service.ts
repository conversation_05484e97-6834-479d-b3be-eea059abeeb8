import { db } from "../firebase"
import {
  collection,
  doc,
  getDoc,
  getDocs,
  updateDoc,
  query,
  where,
  serverTimestamp,
  type Timestamp,
  setDoc,
} from "firebase/firestore"

// Types
export interface UserSubscription {
  id: string // Same as userId for easy lookup
  userId: string
  stripeCustomerId: string
  subscriptionId: string
  subscriptionStatus: "active" | "canceled" | "past_due" | "trialing" | "incomplete" | null
  subscriptionPlan: "free" | "monthly" | "yearly"
  subscriptionCurrentPeriodEnd: Timestamp | number | null
  createdAt: Timestamp | null
  updatedAt: Timestamp | null
}

/**
 * Get a user's subscription
 */
export const getUserSubscription = async (userId: string): Promise<UserSubscription | null> => {
  try {
    const subscriptionDoc = await getDoc(doc(db, "userSubscriptions", userId))

    if (subscriptionDoc.exists()) {
      return { id: subscriptionDoc.id, ...subscriptionDoc.data() } as UserSubscription
    }

    // If no subscription document exists, create a default free subscription
    const defaultSubscription: Omit<UserSubscription, "id"> = {
      userId,
      stripeCustomerId: "",
      subscriptionId: "",
      subscriptionStatus: null,
      subscriptionPlan: "free",
      subscriptionCurrentPeriodEnd: null,
      createdAt: serverTimestamp() as Timestamp,
      updatedAt: serverTimestamp() as Timestamp,
    }

    await setDoc(doc(db, "userSubscriptions", userId), defaultSubscription)

    return { id: userId, ...defaultSubscription } as UserSubscription
  } catch (error) {
    console.error("Error getting user subscription:", error)
    return null
  }
}

/**
 * Update a user's subscription
 */
export const updateUserSubscription = async (
  userId: string,
  subscriptionData: Partial<Omit<UserSubscription, "id" | "userId" | "createdAt">>
) => {
  try {
    const subscriptionRef = doc(db, "userSubscriptions", userId)
    const subscriptionDoc = await getDoc(subscriptionRef)

    if (subscriptionDoc.exists()) {
      // Update existing subscription
      await updateDoc(subscriptionRef, {
        ...subscriptionData,
        updatedAt: serverTimestamp(),
      })
    } else {
      // Create new subscription
      await setDoc(subscriptionRef, {
        userId,
        stripeCustomerId: subscriptionData.stripeCustomerId || "",
        subscriptionId: subscriptionData.subscriptionId || "",
        subscriptionStatus: subscriptionData.subscriptionStatus || null,
        subscriptionPlan: subscriptionData.subscriptionPlan || "free",
        subscriptionCurrentPeriodEnd: subscriptionData.subscriptionCurrentPeriodEnd || null,
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp(),
      })
    }

    return { success: true }
  } catch (error) {
    console.error("Error updating user subscription:", error)
    throw error
  }
}

/**
 * Check if a user has an active subscription
 */
export const hasActiveSubscription = async (userId: string): Promise<boolean> => {
  try {
    const subscription = await getUserSubscription(userId)

    if (!subscription) return false

    return (
      subscription.subscriptionStatus === "active" &&
      (subscription.subscriptionPlan === "monthly" || subscription.subscriptionPlan === "yearly") &&
      (subscription.subscriptionCurrentPeriodEnd
        ? (() => {
            try {
              // Handle Firestore Timestamp objects
              if (
                subscription.subscriptionCurrentPeriodEnd &&
                subscription.subscriptionCurrentPeriodEnd !== null &&
                typeof subscription.subscriptionCurrentPeriodEnd === "object" &&
                typeof (subscription.subscriptionCurrentPeriodEnd as any).toDate === "function"
              ) {
                const endDate = (subscription.subscriptionCurrentPeriodEnd as any).toDate()
                if (endDate && endDate instanceof Date) {
                  return endDate > new Date()
                }
                console.error("toDate() returned invalid date:", endDate)
                return true // Default to true to avoid blocking users
              }
              // Handle numeric timestamps (seconds)
              else if (typeof subscription.subscriptionCurrentPeriodEnd === "number") {
                const timestamp = subscription.subscriptionCurrentPeriodEnd
                // If timestamp is in seconds (before year 2033), convert to milliseconds
                const milliseconds = timestamp < 2000000000 ? timestamp * 1000 : timestamp
                return new Date(milliseconds) > new Date()
              }
              // Handle any other unexpected format
              else {
                console.error(
                  "Unexpected subscriptionCurrentPeriodEnd format:",
                  subscription.subscriptionCurrentPeriodEnd
                )
                return true // Default to true to avoid blocking users
              }
            } catch (error) {
              console.error("Error checking subscription expiration:", error)
              return true // Default to true to avoid blocking users
            }
          })()
        : true)
    )
  } catch (error) {
    console.error("Error checking if user has active subscription:", error)
    return false
  }
}

/**
 * Check if a user is subscribed (with error handling)
 * This is a safer version of hasActiveSubscription that handles errors gracefully
 * and is designed to be used in UI components
 */
export const isUserSubscribed = async (userId: string): Promise<boolean> => {
  try {
    // If no userId is provided, return false
    if (!userId) return false

    const subscription = await getUserSubscription(userId)
    if (!subscription) return false

    // Check if subscription is active
    const isActive =
      subscription.subscriptionStatus === "active" &&
      (subscription.subscriptionPlan === "monthly" || subscription.subscriptionPlan === "yearly")

    if (!isActive) return false

    // If no end date, assume it's valid
    if (!subscription.subscriptionCurrentPeriodEnd) return true

    // Check if subscription is expired based on end date
    try {
      // Handle Firestore Timestamp objects
      if (
        subscription.subscriptionCurrentPeriodEnd &&
        subscription.subscriptionCurrentPeriodEnd !== null &&
        typeof subscription.subscriptionCurrentPeriodEnd === "object" &&
        typeof (subscription.subscriptionCurrentPeriodEnd as any).toDate === "function"
      ) {
        const endDate = (subscription.subscriptionCurrentPeriodEnd as any).toDate()
        if (endDate && endDate instanceof Date) {
          return endDate > new Date()
        }
        console.error("toDate() returned invalid date:", endDate)
        return true // Default to true to avoid blocking users
      }
      // Handle numeric timestamps (seconds)
      else if (typeof subscription.subscriptionCurrentPeriodEnd === "number") {
        const timestamp = subscription.subscriptionCurrentPeriodEnd
        // If timestamp is in seconds (before year 2033), convert to milliseconds
        const milliseconds = timestamp < 2000000000 ? timestamp * 1000 : timestamp
        return new Date(milliseconds) > new Date()
      }
      // Handle any other format (like string dates)
      else if (subscription.subscriptionCurrentPeriodEnd) {
        try {
          // Try to parse as a date string
          return new Date(subscription.subscriptionCurrentPeriodEnd as any) > new Date()
        } catch (e) {
          console.error("Failed to parse subscription end date:", e)
          return true // Default to true to avoid blocking users
        }
      }

      return true // Default to true if we can't determine
    } catch (error) {
      console.error("Error checking subscription expiration:", error)
      return true // Default to true to avoid blocking users
    }
  } catch (error) {
    console.error("Error checking if user is subscribed:", error)
    return false // Default to false on error
  }
}

/**
 * Migrate user subscription data from users collection to userSubscriptions collection
 * This is a one-time migration function
 */
export const migrateUserSubscriptions = async (userId: string) => {
  try {
    // Get user document
    const userDoc = await getDoc(doc(db, "users", userId))

    if (!userDoc.exists()) {
      console.error("User document does not exist")
      return { success: false, error: "User document does not exist" }
    }

    const userData = userDoc.data()

    // Create subscription document
    const subscriptionData: Omit<UserSubscription, "id"> = {
      userId,
      stripeCustomerId: userData.stripeCustomerId || "",
      subscriptionId: userData.subscriptionId || "",
      subscriptionStatus: userData.subscriptionStatus || null,
      subscriptionPlan: userData.subscriptionPlan || "free",
      subscriptionCurrentPeriodEnd: userData.subscriptionCurrentPeriodEnd || null,
      createdAt: serverTimestamp() as Timestamp,
      updatedAt: serverTimestamp() as Timestamp,
    }

    await setDoc(doc(db, "userSubscriptions", userId), subscriptionData)

    return { success: true }
  } catch (error) {
    console.error("Error migrating user subscription:", error)
    return { success: false, error }
  }
}
