// Legacy squad service - now delegates to the new domain service
import { SquadService } from "@/lib/domains/squad/squad.service"
import { Squad } from "@/lib/domains/squad/squad.types"
import { Timestamp } from "firebase/firestore"

// Legacy interface for backward compatibility
export interface LegacySquad {
  id: string
  name: string
  description?: string
  leaderId: string
  members: string[] // Array of user IDs - deprecated
  createdAt: Timestamp | null
}

// Legacy squad operations - now delegate to new domain service
export const createSquad = async (squadData: Omit<LegacySquad, "id" | "createdAt">) => {
  try {
    return await SquadService.createSquad({
      name: squadData.name,
      description: squadData.description,
      leaderId: squadData.leaderId,
    })
  } catch (error) {
    console.error("Error creating squad:", error)
    throw error
  }
}

export const getSquad = async (squadId: string): Promise<LegacySquad | null> => {
  try {
    const squad = await SquadService.getSquad(squadId)
    if (!squad) return null

    // Get squad members to populate the legacy members array
    const squadMembers = await SquadService.getSquadMembers(squadId)
    const memberIds = squadMembers.map((member) => member.userId)

    return {
      ...squad,
      members: memberIds, // Legacy format
      createdAt: squad.createdAt as Timestamp,
    } as LegacySquad
  } catch (error) {
    console.error("Error getting squad:", error)
    throw error
  }
}

export const getUserSquads = async (userId: string): Promise<LegacySquad[]> => {
  try {
    const squads = await SquadService.getUserSquads(userId)

    // Convert to legacy format with members arrays
    const legacySquads: LegacySquad[] = []
    for (const squad of squads) {
      const squadMembers = await SquadService.getSquadMembers(squad.id)
      const memberIds = squadMembers.map((member) => member.userId)

      legacySquads.push({
        ...squad,
        members: memberIds, // Legacy format
        createdAt: squad.createdAt as Timestamp,
      } as LegacySquad)
    }

    return legacySquads
  } catch (error) {
    console.error("Error getting user squads:", error)
    throw error
  }
}

export const updateSquad = async (squadId: string, squadData: Partial<LegacySquad>) => {
  try {
    // Filter out members array from updates since it's handled differently now
    const { members, ...updateData } = squadData
    const result = await SquadService.updateSquad(squadId, updateData)
    return { success: result.success }
  } catch (error) {
    console.error("Error updating squad:", error)
    throw error
  }
}

export const addMemberToSquad = async (squadId: string, userId: string) => {
  try {
    const result = await SquadService.addMember(squadId, userId)
    return { success: result.success }
  } catch (error) {
    console.error("Error adding member to squad:", error)
    throw error
  }
}

export const removeMemberFromSquad = async (squadId: string, userId: string) => {
  try {
    const result = await SquadService.removeMember(squadId, userId)
    return { success: result.success }
  } catch (error) {
    console.error("Error removing member from squad:", error)
    throw error
  }
}

// Export the new Squad type for backward compatibility
export type { Squad }
