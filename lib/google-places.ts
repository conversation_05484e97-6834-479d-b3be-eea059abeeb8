import { db, auth } from "./firebase"
import {
  collection,
  query,
  where,
  getDocs,
  addDoc,
  serverTimestamp,
  doc,
  updateDoc,
} from "firebase/firestore"
import { GooglePlaceImage } from "@/lib/domains/trip/trip.types"

// Helper function to get the authentication token
async function getAuthToken() {
  try {
    const currentUser = auth.currentUser
    if (!currentUser) {
      throw new Error("User not authenticated")
    }

    const token = await currentUser.getIdToken()
    return token
  } catch (error) {
    console.error("Error getting auth token:", error)
    throw error
  }
}

// Note: Direct client-side URL generation for Google Places Photo API is not recommended
// due to security concerns (exposing API keys) and CORS restrictions.
// Use the server-side API endpoint at /api/images/google-places instead.

// Types
export interface LocationImage {
  id?: string
  location: string
  imageUrl: string
  source: "google" | "custom"
  sourceId?: string
  createdAt?: any
  updatedAt?: any
  attribution?: {
    name: string
    photoReference: string
  }
  googlePlaceImage?: {
    photoReference: string
    placeId: string
  }
}

/**
 * Checks if an image URL is accessible (not returning 4XX errors)
 * @param imageUrl The image URL to check
 * @returns Promise<boolean> true if accessible, false if 4XX error
 */
export async function isImageUrlAccessible(imageUrl: string): Promise<boolean> {
  try {
    // Use our server-side API to validate the image URL
    const response = await fetch("/api/images/validate-url", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({ imageUrl }),
    })

    if (!response.ok) {
      console.error("Server-side validation failed:", response.status)
      return false
    }

    const data = await response.json()
    return data.isAccessible
  } catch (error) {
    console.error("Error checking image URL accessibility:", error)
    // If we can't check, assume it's not accessible
    return false
  }
}

/**
 * Client-side only validation that skips the server check
 * This is used when we know the image failed to load in the browser
 * @param cachedImage The cached location image data
 * @returns Promise<string> The refreshed image URL or original URL
 */
export async function refreshImageUrlDirectly(cachedImage: LocationImage): Promise<string> {
  try {
    // Skip accessibility check since we know the image failed to load
    // Go directly to refresh if we have googlePlaceImage data
    if (cachedImage.googlePlaceImage?.photoReference && cachedImage.googlePlaceImage?.placeId) {
      console.log(`Image failed to load for ${cachedImage.location}, attempting to refresh...`)

      const newImageUrl = await refreshGooglePlaceImageUrl(
        cachedImage.googlePlaceImage.photoReference,
        cachedImage.googlePlaceImage.placeId
      )

      if (newImageUrl && cachedImage.id) {
        // Update the cached image with the new URL
        const updateSuccess = await updateLocationImageUrl(cachedImage.id, newImageUrl)

        if (updateSuccess) {
          console.log(`Successfully refreshed image URL for ${cachedImage.location}`)
          return newImageUrl
        }
      }
    }

    // If we can't refresh, return the original URL
    console.warn(`Could not refresh image URL for ${cachedImage.location}, using original URL`)
    return cachedImage.imageUrl
  } catch (error) {
    console.error("Error refreshing image URL directly:", error)
    // Return the original URL as fallback
    return cachedImage.imageUrl
  }
}

/**
 * Refreshes a Google Places image URL using photoReference and placeId
 * @param photoReference The Google Places photo reference
 * @param placeId The Google Places place ID
 * @returns Promise<string | null> New image URL or null if failed
 */
export async function refreshGooglePlaceImageUrl(
  photoReference: string,
  placeId: string
): Promise<string | null> {
  try {
    const response = await fetch(
      `/api/images/google-places-url?photoReference=${encodeURIComponent(photoReference)}&placeId=${encodeURIComponent(placeId)}`
    )

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }

    const data = await response.json()
    return data.imageUrl
  } catch (error) {
    console.error("Error refreshing Google Places image URL:", error)
    return null
  }
}

/**
 * Updates a cached location image with a new URL
 * @param locationImageId The ID of the location image document to update
 * @param newImageUrl The new image URL
 * @returns Promise<boolean> true if successful, false otherwise
 */
export async function updateLocationImageUrl(
  locationImageId: string,
  newImageUrl: string
): Promise<boolean> {
  try {
    const docRef = doc(db, "locationImages", locationImageId)
    await updateDoc(docRef, {
      imageUrl: newImageUrl,
      updatedAt: serverTimestamp(),
    })
    return true
  } catch (error) {
    console.error("Error updating location image URL:", error)
    return false
  }
}

/**
 * Validates and potentially refreshes a cached location image URL
 * If the URL returns a 4XX error and we have googlePlaceImage data, it will refresh the URL
 * @param cachedImage The cached location image data
 * @returns Promise<string> The validated or refreshed image URL
 */
export async function validateAndRefreshImageUrl(cachedImage: LocationImage): Promise<string> {
  try {
    // First check if the current URL is accessible
    const isAccessible = await isImageUrlAccessible(cachedImage.imageUrl)

    if (isAccessible) {
      // URL is still good, return it
      return cachedImage.imageUrl
    }

    // URL is not accessible (4XX error), try to refresh if we have googlePlaceImage data
    if (cachedImage.googlePlaceImage?.photoReference && cachedImage.googlePlaceImage?.placeId) {
      console.log(`Image URL not accessible for ${cachedImage.location}, attempting to refresh...`)

      const newImageUrl = await refreshGooglePlaceImageUrl(
        cachedImage.googlePlaceImage.photoReference,
        cachedImage.googlePlaceImage.placeId
      )

      if (newImageUrl && cachedImage.id) {
        // Update the cached image with the new URL
        const updateSuccess = await updateLocationImageUrl(cachedImage.id, newImageUrl)

        if (updateSuccess) {
          console.log(`Successfully refreshed image URL for ${cachedImage.location}`)
          return newImageUrl
        }
      }
    }

    // If we can't refresh, return the original URL (it might work for the user)
    console.warn(`Could not refresh image URL for ${cachedImage.location}, using original URL`)
    return cachedImage.imageUrl
  } catch (error) {
    console.error("Error validating and refreshing image URL:", error)
    // Return the original URL as fallback
    return cachedImage.imageUrl
  }
}

/**
 * Searches for cached images for a location
 * @param location The location to search for
 * @returns The image URL if found, null otherwise
 */
export async function findCachedLocationImage(location: string): Promise<string | null> {
  try {
    // Normalize the location for consistent searching
    const normalizedLocation = location.toLowerCase().trim()

    // Query the LocationImages collection
    const imagesRef = collection(db, "locationImages")
    const q = query(imagesRef, where("location", "==", normalizedLocation))
    const querySnapshot = await getDocs(q)

    if (querySnapshot.empty) {
      return null
    }

    // Return the first image URL found
    const imageDoc = querySnapshot.docs[0]
    const imageData = imageDoc.data() as LocationImage
    return imageData.imageUrl
  } catch (error) {
    console.error("Error finding cached location image:", error)
    return null
  }
}

/**
 * Searches for cached images for a location and returns the full image data including attribution
 * @param location The location to search for
 * @returns The image data if found, null otherwise
 */
export async function findCachedLocationImageWithAttribution(
  location: string
): Promise<LocationImage | null> {
  try {
    // Normalize the location for consistent searching
    const normalizedLocation = location.toLowerCase().trim()

    // Query the LocationImages collection
    const imagesRef = collection(db, "locationImages")
    const q = query(imagesRef, where("location", "==", normalizedLocation))
    const querySnapshot = await getDocs(q)

    if (querySnapshot.empty) {
      return null
    }

    // Return the first image found
    const imageDoc = querySnapshot.docs[0]
    return { id: imageDoc.id, ...imageDoc.data() } as LocationImage
  } catch (error) {
    console.error("Error finding cached location image with attribution:", error)
    return null
  }
}

/**
 * Saves a location image to the cache
 * @param locationImage The location image data to save
 * @returns The ID of the saved document
 */
export async function saveLocationImage(
  locationImage: Omit<LocationImage, "id" | "createdAt">
): Promise<string> {
  try {
    // Normalize the location
    const normalizedLocation = locationImage.location.toLowerCase().trim()

    const newImage = {
      ...locationImage,
      location: normalizedLocation,
      createdAt: serverTimestamp(),
    }

    const docRef = await addDoc(collection(db, "locationImages"), newImage)
    return docRef.id
  } catch (error) {
    console.error("Error saving location image:", error)
    throw error
  }
}

/**
 * Fetches an image for a location from Google Places API using a place ID
 * @param placeId The Google Places ID to fetch an image for
 * @param locationName The name of the location (used as a fallback)
 * @returns The image URL and attribution if found, null otherwise
 */
export async function fetchGooglePlacesImageByPlaceId(
  placeId: string,
  locationName?: string
): Promise<{
  imageUrl: string
  sourceId: string
  attribution?: {
    name: string
    photoReference: string
  }
} | null> {
  try {
    // Get authentication token
    const token = await getAuthToken()

    // Call our API endpoint that handles Google Places API requests
    // Include both placeId and query (location name) for better results
    const url = new URL("/api/images/google-places", window.location.origin)
    url.searchParams.append("placeId", placeId)
    if (locationName) {
      url.searchParams.append("query", locationName)
    }

    const response = await fetch(url.toString(), {
      headers: {
        Authorization: `Bearer ${token}`,
      },
    })

    if (!response.ok) {
      throw new Error(`Google Places API error: ${response.statusText}`)
    }

    const data = await response.json()

    if (!data.imageUrl) {
      return null
    }

    return {
      imageUrl: data.imageUrl,
      sourceId: data.placeId || "",
      attribution: data.attribution
        ? {
            name: data.attribution.name,
            photoReference: data.attribution.photoReference,
          }
        : undefined,
    }
  } catch (error) {
    console.error("Error fetching Google Places image by place ID:", error)
    return null
  }
}

/**
 * Fetches an image for a location from Google Places API using a text query
 * @param location The location to fetch an image for
 * @returns The image URL and attribution if found, null otherwise
 */
export async function fetchGooglePlacesImage(location: string): Promise<{
  imageUrl: string
  sourceId: string
  attribution?: {
    name: string
    photoReference: string
  }
} | null> {
  try {
    // Get authentication token
    const token = await getAuthToken()

    // Call our API endpoint that handles Google Places API requests
    const response = await fetch(
      `/api/images/google-places?query=${encodeURIComponent(location)}`,
      {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      }
    )

    if (!response.ok) {
      throw new Error(`Google Places API error: ${response.statusText}`)
    }

    const data = await response.json()

    if (!data.imageUrl) {
      return null
    }

    return {
      imageUrl: data.imageUrl,
      sourceId: data.placeId || "",
      attribution: data.attribution
        ? {
            name: data.attribution.name,
            photoReference: data.attribution.photoReference,
          }
        : undefined,
    }
  } catch (error) {
    console.error("Error fetching Google Places image:", error)
    return null
  }
}

/**
 * Fetches a GooglePlaceImage object for a location from Google Places API using a place ID
 * @param placeId The Google Places ID to fetch an image for
 * @param locationName The name of the location (used as a fallback)
 * @returns GooglePlaceImage object if found, null otherwise
 */
export async function fetchGooglePlaceImageByPlaceId(
  placeId: string,
  locationName?: string
): Promise<GooglePlaceImage | null> {
  try {
    const result = await fetchGooglePlacesImageByPlaceId(placeId, locationName)

    if (result?.attribution?.photoReference && result.sourceId) {
      return {
        photoReference: result.attribution.photoReference,
        placeId: result.sourceId,
      }
    }

    return null
  } catch (error) {
    console.error("Error fetching Google Place image by place ID:", error)
    return null
  }
}

/**
 * Fetches a GooglePlaceImage object for a location from Google Places API using a text query
 * @param location The location to fetch an image for
 * @returns GooglePlaceImage object if found, null otherwise
 */
export async function fetchGooglePlaceImage(location: string): Promise<GooglePlaceImage | null> {
  try {
    const result = await fetchGooglePlacesImage(location)

    if (result?.attribution?.photoReference && result.sourceId) {
      return {
        photoReference: result.attribution.photoReference,
        placeId: result.sourceId,
      }
    }

    return null
  } catch (error) {
    console.error("Error fetching Google Place image:", error)
    return null
  }
}

/**
 * Gets or fetches an image for a location using a place ID
 * First checks the cache, then falls back to Google Places API if needed
 * @param placeId The Google Places ID to get an image for
 * @param locationName Optional location name for caching
 * @returns The image URL and attribution information
 */
export async function getLocationImageByPlaceId(
  placeId: string,
  locationName?: string
): Promise<{
  url: string
  attribution?: {
    name: string
    photoReference: string
  }
}> {
  try {
    // Default placeholder image
    const placeholderImage = "/placeholder.svg?height=300&width=600"

    if (!placeId) {
      return { url: placeholderImage }
    }

    // First, check our cache using the location name or placeId
    const searchLocation = locationName || placeId
    try {
      const cachedImageDoc = await findCachedLocationImageWithAttribution(searchLocation)
      if (cachedImageDoc) {
        // Validate and potentially refresh the cached image URL
        const validatedUrl = await validateAndRefreshImageUrl(cachedImageDoc)
        return {
          url: validatedUrl,
          attribution: cachedImageDoc.attribution,
        }
      }
    } catch (cacheError) {
      // If there's an error accessing the cache, log and continue to API call
      console.warn("Error accessing image cache, falling back to direct API call:", cacheError)
    }

    // If not in cache, fetch from Google Places API
    const googlePlacesResult = await fetchGooglePlacesImageByPlaceId(placeId, locationName)
    if (!googlePlacesResult) {
      return { url: placeholderImage }
    }

    try {
      // Try to save to cache for future use
      await saveLocationImage({
        location: locationName || googlePlacesResult.attribution?.name || placeId,
        imageUrl: googlePlacesResult.imageUrl,
        source: "google",
        sourceId: googlePlacesResult.sourceId,
        attribution: googlePlacesResult.attribution,
        googlePlaceImage:
          googlePlacesResult.attribution?.photoReference && googlePlacesResult.sourceId
            ? {
                photoReference: googlePlacesResult.attribution.photoReference,
                placeId: googlePlacesResult.sourceId,
              }
            : undefined,
      })
    } catch (saveError) {
      // If there's an error saving to cache, log but don't fail the whole operation
      console.warn("Error saving location image to cache:", saveError)
    }

    return {
      url: googlePlacesResult.imageUrl,
      attribution: googlePlacesResult.attribution,
    }
  } catch (error) {
    console.error("Error getting location image by place ID:", error)
    return { url: "/placeholder.svg?height=300&width=600" }
  }
}

/**
 * Enhanced place result interface
 */
export interface EnhancedPlaceResult {
  placeId: string
  name: string
  formattedAddress: string
  location?: {
    lat: number
    lng: number
  }
  types?: string[]
  hasPhoto: boolean
  // Enhanced data for Pro users
  rating?: number | null
  userRatingsTotal?: number | null
  priceLevel?: number | null
  businessStatus?: string | null
  openingHours?: {
    openNow?: boolean | null
    weekdayText?: string[] | null
  } | null
}

/**
 * Search options for enhanced places search
 */
export interface PlacesSearchOptions {
  location?: { lat: number; lng: number }
  radius?: number // in meters, max 50000 (50km)
  type?: string
  includeRatings?: boolean
}

/**
 * Searches for places using the Google Places API
 * @param query The search query
 * @param options Enhanced search options (Pro features)
 * @returns An array of place results
 */
export async function searchPlaces(
  query: string,
  options?: PlacesSearchOptions
): Promise<EnhancedPlaceResult[]> {
  try {
    // Get authentication token
    const token = await getAuthToken()

    // Build query parameters
    const params = new URLSearchParams({
      query: query,
    })

    if (options?.location) {
      params.append("location", `${options.location.lat},${options.location.lng}`)
    }

    if (options?.radius) {
      params.append("radius", options.radius.toString())
    }

    if (options?.type) {
      params.append("type", options.type)
    }

    if (options?.includeRatings) {
      params.append("includeRatings", "true")
    }

    const response = await fetch(`/api/places/search?${params.toString()}`, {
      headers: {
        Authorization: `Bearer ${token}`,
      },
    })

    if (!response.ok) {
      throw new Error(`Google Places API error: ${response.statusText}`)
    }

    const data = await response.json()

    if (!data.places || !Array.isArray(data.places)) {
      return []
    }

    return data.places
  } catch (error) {
    console.error("Error searching for places:", error)
    return []
  }
}

/**
 * Search for activity-specific places (restaurants, entertainment, attractions)
 * @param query The search query
 * @param destination The destination city/location
 * @param activityType Type of activity (restaurant, entertainment, attraction)
 * @param location Optional location coordinates for Pro users
 * @returns Enhanced place results with ratings and details
 */
export async function searchActivityPlaces(
  query: string,
  destination: string,
  activityType: "restaurant" | "entertainment" | "attraction" | "dining",
  location?: { lat: number; lng: number }
): Promise<EnhancedPlaceResult[]> {
  try {
    // Get authentication token
    const token = await getAuthToken()

    const response = await fetch("/api/places/activity-search", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
      body: JSON.stringify({
        query,
        destination,
        activityType,
        location: location ? `${location.lat},${location.lng}` : undefined,
      }),
    })

    if (!response.ok) {
      throw new Error(`Activity Places API error: ${response.statusText}`)
    }

    const data = await response.json()

    if (!data.places || !Array.isArray(data.places)) {
      return []
    }

    return data.places
  } catch (error) {
    console.error("Error searching for activity places:", error)
    return []
  }
}

/**
 * Gets or fetches an image for a location
 * First checks the cache, then falls back to Google Places API if needed
 * @param location The location to get an image for
 * @returns The image URL and attribution information
 */
export async function getLocationImage(location: string): Promise<{
  url: string
  attribution?: {
    name: string
    photoReference: string
  }
}> {
  try {
    // Default placeholder image
    const placeholderImage = "/placeholder.svg?height=300&width=600"

    if (!location) {
      return { url: placeholderImage }
    }

    try {
      // First, check our cache
      const cachedImageDoc = await findCachedLocationImageWithAttribution(location)
      if (cachedImageDoc) {
        // Validate and potentially refresh the cached image URL
        const validatedUrl = await validateAndRefreshImageUrl(cachedImageDoc)
        return {
          url: validatedUrl,
          attribution: cachedImageDoc.attribution,
        }
      }
    } catch (cacheError) {
      // If there's an error accessing the cache (e.g., permissions), log and continue
      console.warn("Error accessing image cache, falling back to direct API call:", cacheError)
    }

    // For now, skip GooglePlaceImage URL generation and use the existing server-side approach
    // TODO: Implement proper server-side URL generation for GooglePlaceImage data

    // Fallback to legacy method
    const googlePlacesResult = await fetchGooglePlacesImage(location)
    if (!googlePlacesResult) {
      return { url: placeholderImage }
    }

    try {
      // Try to save to cache for future use
      await saveLocationImage({
        location,
        imageUrl: googlePlacesResult.imageUrl,
        source: "google",
        sourceId: googlePlacesResult.sourceId,
        attribution: googlePlacesResult.attribution,
        googlePlaceImage:
          googlePlacesResult.attribution?.photoReference && googlePlacesResult.sourceId
            ? {
                photoReference: googlePlacesResult.attribution.photoReference,
                placeId: googlePlacesResult.sourceId,
              }
            : undefined,
      })
    } catch (saveError) {
      // If there's an error saving to cache, log but don't fail the whole operation
      console.warn("Error saving location image to cache:", saveError)
    }

    return {
      url: googlePlacesResult.imageUrl,
      attribution: googlePlacesResult.attribution,
    }
  } catch (error) {
    console.error("Error getting location image:", error)
    return { url: "/placeholder.svg?height=300&width=600" }
  }
}
