"use server"

import { put, del } from "@vercel/blob"
import { verifyAuthToken, getAdminInstance } from "@/lib/firebase-admin"

// Maximum file size: 1MB
const MAX_FILE_SIZE = 1 * 1024 * 1024

// Allowed file types
const ALLOWED_TYPES = ["image/jpeg", "image/jpg", "image/png", "image/webp"]

export interface ManageProfilePictureResult {
  success: boolean
  url?: string
  error?: string
}

export async function uploadProfilePictureAction(
  authToken: string,
  formData: FormData
): Promise<ManageProfilePictureResult> {
  try {
    // Verify the auth token
    const authResult = await verifyAuthToken(authToken)
    if (!authResult.isValid || !authResult.uid) {
      return {
        success: false,
        error: "You must be logged in to upload a profile picture",
      }
    }

    const userId = authResult.uid
    console.log("Upload Profile Picture Action:", { userId })

    const file = formData.get("file") as File
    console.log("File:", { name: file?.name, size: file?.size, type: file?.type })

    // Validate required fields
    if (!file) {
      console.log("Validation failed: No file provided")
      return {
        success: false,
        error: "No file provided",
      }
    }

    // Validate file type
    if (!ALLOWED_TYPES.includes(file.type)) {
      console.log("Validation failed: Invalid file type:", file.type)
      return {
        success: false,
        error: "Invalid file type. Only JPEG, PNG, and WebP images are allowed.",
      }
    }

    // Validate file size
    if (file.size > MAX_FILE_SIZE) {
      console.log("Validation failed: File too large:", file.size)
      return {
        success: false,
        error: "File size too large. Maximum size is 1MB.",
      }
    }

    // Check if Vercel Blob token is available
    if (!process.env.BLOB_READ_WRITE_TOKEN) {
      console.error("BLOB_READ_WRITE_TOKEN is not configured")
      return {
        success: false,
        error: "Storage service is not configured",
      }
    }

    // Generate a unique filename
    const timestamp = Date.now()
    const fileExtension = file.name.split(".").pop() || "jpg"
    const filename = `profile-pictures/${userId}/${timestamp}.${fileExtension}`
    console.log("Generated filename:", filename)

    try {
      console.log("Starting Vercel Blob upload...")

      // Upload to Vercel Blob
      const blob = await put(filename, file, {
        access: "public",
        token: process.env.BLOB_READ_WRITE_TOKEN,
      })

      console.log("Vercel Blob upload successful:", blob.url)

      // Update user document in Firestore using Admin SDK
      const { adminDb, adminFieldValue } = await getAdminInstance()
      if (!adminDb || !adminFieldValue) {
        throw new Error("Firebase Admin is not initialized")
      }

      await adminDb.collection("users").doc(userId).update({
        photoURL: blob.url,
        updatedAt: adminFieldValue.serverTimestamp(),
      })

      console.log("User document updated with new profile picture URL")

      return {
        success: true,
        url: blob.url,
      }
    } catch (uploadError) {
      console.error("Vercel Blob upload error details:", {
        error: uploadError,
        message: uploadError instanceof Error ? uploadError.message : "Unknown error",
        stack: uploadError instanceof Error ? uploadError.stack : undefined,
        filename,
        fileSize: file.size,
        fileType: file.type,
      })

      // Provide more specific error messages based on the error type
      let errorMessage = "Failed to upload image to storage"
      if (uploadError instanceof Error) {
        if (uploadError.message.includes("token")) {
          errorMessage = "Storage authentication failed"
        } else if (uploadError.message.includes("size")) {
          errorMessage = "File size exceeds storage limits"
        } else if (
          uploadError.message.includes("network") ||
          uploadError.message.includes("fetch")
        ) {
          errorMessage = "Network error during upload"
        }
      }

      return {
        success: false,
        error: errorMessage,
      }
    }
  } catch (error) {
    console.error("Profile picture upload error details:", {
      error,
      message: error instanceof Error ? error.message : "Unknown error",
      stack: error instanceof Error ? error.stack : undefined,
    })

    return {
      success: false,
      error: "Internal server error",
    }
  }
}

export async function removeProfilePictureAction(
  authToken: string,
  currentPhotoURL?: string
): Promise<ManageProfilePictureResult> {
  try {
    // Verify the auth token
    const authResult = await verifyAuthToken(authToken)
    if (!authResult.isValid || !authResult.uid) {
      return {
        success: false,
        error: "You must be logged in to remove a profile picture",
      }
    }

    const userId = authResult.uid
    console.log("Remove Profile Picture Action:", { userId, currentPhotoURL })

    // If there's a current photo URL and it's from our blob storage, delete it
    if (currentPhotoURL && currentPhotoURL.includes("blob.vercel-storage.com")) {
      try {
        await del(currentPhotoURL, {
          token: process.env.BLOB_READ_WRITE_TOKEN,
        })
        console.log("Old profile picture deleted from blob storage")
      } catch (deleteError) {
        console.warn("Failed to delete old profile picture from blob storage:", deleteError)
        // Continue with the operation even if deletion fails
      }
    }

    // Update user document in Firestore to remove photoURL using Admin SDK
    const { adminDb, adminFieldValue } = await getAdminInstance()
    if (!adminDb || !adminFieldValue) {
      throw new Error("Firebase Admin is not initialized")
    }

    await adminDb.collection("users").doc(userId).update({
      photoURL: null,
      updatedAt: adminFieldValue.serverTimestamp(),
    })

    console.log("User document updated to remove profile picture URL")

    return {
      success: true,
    }
  } catch (error) {
    console.error("Profile picture removal error details:", {
      error,
      message: error instanceof Error ? error.message : "Unknown error",
      stack: error instanceof Error ? error.stack : undefined,
      currentPhotoURL,
    })

    return {
      success: false,
      error: "Internal server error",
    }
  }
}
