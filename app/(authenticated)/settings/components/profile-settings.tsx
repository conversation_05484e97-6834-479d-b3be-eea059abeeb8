"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Upload, Trash2, Loader2, Camera, X } from "lucide-react"
import {
  useUserData,
  useUserDataLoading,
  useUpdateUserData,
  useUser,
} from "@/lib/domains/auth/auth.hooks"
import { useProfilePictureManager } from "@/lib/domains/user/user.hooks"
import { toast } from "@/components/ui/use-toast"
import { getBestAvatar, getInitials } from "@/lib/utils/avatar-utils"

export function ProfileSettings() {
  const userData = useUserData()
  const userDataLoading = useUserDataLoading()
  const updateUser = useUpdateUserData()
  const user = useUser()
  const { uploadProfilePicture, removeProfilePicture, uploading, removing } =
    useProfilePictureManager()

  const handleProfilePictureUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (!file || !user?.uid) return

    try {
      const result = await uploadProfilePicture(file)

      if (!result.success) {
        toast({
          title: "Upload failed",
          description: result.error || "Failed to upload profile picture.",
          variant: "destructive",
        })
      }
      // Success toast is handled by the hook
    } catch (error) {
      console.error("Error uploading profile picture:", error)
      toast({
        title: "Upload failed",
        description: "Something went wrong. Please try again.",
        variant: "destructive",
      })
    } finally {
      // Reset file input
      e.target.value = ""
    }
  }

  const handleProfilePictureRemove = async () => {
    if (!user?.uid) return

    try {
      const result = await removeProfilePicture(userData?.photoURL || undefined)

      if (!result.success) {
        toast({
          title: "Removal failed",
          description: result.error || "Failed to remove profile picture.",
          variant: "destructive",
        })
      }
      // Success toast is handled by the hook
    } catch (error) {
      console.error("Error removing profile picture:", error)
      toast({
        title: "Removal failed",
        description: "Something went wrong. Please try again.",
        variant: "destructive",
      })
    }
  }

  const saveProfileChanges = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!userData || !user) return

    try {
      const formElement = e.target as HTMLFormElement
      const formData = new FormData(formElement)

      const updatedData = {
        displayName: formData.get("fullName") as string,
        email: formData.get("email") as string,
        bio: formData.get("bio") as string,
      }

      // Use the auth store's updateUserData function with showToast=true
      updateUser(updatedData)
    } catch (error) {
      console.error("Error updating profile:", error)
      // Toast is already shown by the updateUserData function
    }
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Profile Information</CardTitle>
        <CardDescription>Update your personal information</CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {userDataLoading ? (
          <div className="flex flex-col items-center justify-center py-8">
            <Loader2 className="h-8 w-8 animate-spin text-primary mb-4" />
            <p className="text-sm text-muted-foreground">Loading profile information...</p>
          </div>
        ) : (
          userData && (
            <form id="profileForm" onSubmit={saveProfileChanges}>
              <div className="flex flex-col md:flex-row gap-6">
                <div className="flex flex-col items-center gap-4">
                  <div className="relative">
                    <Avatar className="h-24 w-24">
                      <AvatarImage
                        src={getBestAvatar(userData?.photoURL, userData?.displayName, 96)}
                        alt={userData?.displayName || "User"}
                      />
                      <AvatarFallback>{getInitials(userData?.displayName)}</AvatarFallback>
                    </Avatar>
                    {(uploading || removing) && (
                      <div className="absolute inset-0 bg-black/50 rounded-full flex items-center justify-center">
                        <Loader2 className="h-6 w-6 animate-spin text-white" />
                      </div>
                    )}
                  </div>
                  <div className="flex gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      type="button"
                      disabled={uploading || removing}
                      onClick={() => document.getElementById("profilePictureInput")?.click()}
                    >
                      {uploading ? (
                        <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                      ) : (
                        <Upload className="h-4 w-4 mr-2" />
                      )}
                      Upload
                    </Button>
                    <input
                      id="profilePictureInput"
                      type="file"
                      accept="image/jpeg,image/jpg,image/png,image/webp"
                      onChange={handleProfilePictureUpload}
                      className="hidden"
                    />
                    {userData?.photoURL && (
                      <Button
                        variant="outline"
                        size="sm"
                        className="text-destructive"
                        type="button"
                        disabled={uploading || removing}
                        onClick={handleProfilePictureRemove}
                      >
                        {removing ? (
                          <Loader2 className="h-4 w-4 animate-spin" />
                        ) : (
                          <Trash2 className="h-4 w-4" />
                        )}
                      </Button>
                    )}
                  </div>
                </div>

                <div className="flex-1 space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="fullName">Full Name</Label>
                      <Input
                        id="fullName"
                        name="fullName"
                        defaultValue={userData?.displayName || ""}
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="email">Email</Label>
                      <Input
                        id="email"
                        name="email"
                        type="email"
                        defaultValue={userData?.email || ""}
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="phone">Phone Number</Label>
                      <Input id="phone" name="phone" type="tel" defaultValue="" />
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="bio">Bio</Label>
                    <textarea
                      id="bio"
                      name="bio"
                      rows={3}
                      defaultValue={userData?.bio || ""}
                      className="flex w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                    ></textarea>
                  </div>
                </div>
              </div>
            </form>
          )
        )}
      </CardContent>
      <CardFooter>
        <Button type="submit" form="profileForm" disabled={userDataLoading || !userData}>
          {userDataLoading ? "Saving..." : "Save Changes"}
        </Button>
      </CardFooter>
    </Card>
  )
}
