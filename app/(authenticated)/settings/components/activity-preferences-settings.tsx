"use client"

import React, { useState, use<PERSON><PERSON>back, useMemo } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Checkbox } from "@/components/ui/checkbox"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Label } from "@/components/ui/label"
import { Badge } from "@/components/ui/badge"
import { useToast } from "@/components/ui/use-toast"
import { Loader2, Crown } from "lucide-react"
import {
  useActivityPreferences,
  useUpdateActivityPreferences,
  PREFERENCE_OPTIONS,
  ActivityPreferencesUpdateData,
} from "@/lib/domains/activity-preferences"
import { useIsUserSubscribed } from "@/lib/domains/user-subscription"

/**
 * Upgrade prompt component for free users
 */
const UpgradePrompt = React.memo(() => (
  <Card>
    <CardHeader>
      <CardTitle className="flex items-center gap-2">
        <Crown className="h-5 w-5 text-yellow-500" />
        Activity Preferences
      </CardTitle>
      <CardDescription>
        Customize your dining, shopping, and entertainment preferences to get personalized itinerary
        suggestions.
      </CardDescription>
    </CardHeader>
    <CardContent>
      <div className="text-center py-8">
        <Crown className="h-12 w-12 text-yellow-500 mx-auto mb-4" />
        <h3 className="text-lg font-semibold mb-2">Pro Feature</h3>
        <p className="text-muted-foreground mb-4">
          Activity preferences are available for Pro subscribers only.
        </p>
        <Button
          className="bg-gradient-to-r from-yellow-400 to-orange-500 hover:from-yellow-500 hover:to-orange-600 text-white"
          onClick={() => (window.location.href = "/settings?tab=billing")}
        >
          Upgrade to Pro
        </Button>
      </div>
    </CardContent>
  </Card>
))

UpgradePrompt.displayName = "UpgradePrompt"

/**
 * Multi-select checkbox component
 */
interface MultiSelectProps {
  options: readonly string[]
  selected: string[]
  onChange: (selected: string[]) => void
  title: string
}

const MultiSelect = React.memo<MultiSelectProps>(({ options, selected, onChange, title }) => {
  const handleToggle = useCallback(
    (option: string) => {
      const newSelected = selected.includes(option)
        ? selected.filter((item) => item !== option)
        : [...selected, option]
      onChange(newSelected)
    },
    [selected, onChange]
  )

  return (
    <div className="space-y-3">
      <h4 className="font-medium">{title}</h4>
      <div className="grid grid-cols-2 gap-2">
        {options.map((option) => (
          <div key={option} className="flex items-center space-x-2">
            <Checkbox
              id={`${title}-${option}`}
              checked={selected.includes(option)}
              onCheckedChange={() => handleToggle(option)}
            />
            <Label htmlFor={`${title}-${option}`} className="text-sm cursor-pointer">
              {option}
            </Label>
          </div>
        ))}
      </div>
    </div>
  )
})

MultiSelect.displayName = "MultiSelect"

/**
 * Activity preferences settings component
 */
export const ActivityPreferencesSettings = React.memo(() => {
  const isSubscribed = useIsUserSubscribed()
  const { preferences, loading: preferencesLoading } = useActivityPreferences()
  const { updatePreferences, loading: saveLoading } = useUpdateActivityPreferences()
  const { toast } = useToast()

  // Local state for form data
  const [formData, setFormData] = useState({
    eateries: {
      cuisineTypes: [] as string[],
      diningExperience: [] as string[],
      dietaryNeeds: [] as string[],
    },
    shopping: {
      style: [] as string[],
      budget: "mid-range" as string,
      focusAreas: [] as string[],
    },
    entertainment: {
      venues: [] as string[],
      vibe: [] as string[],
      interests: [] as string[],
    },
  })

  // Update form data when preferences load
  React.useEffect(() => {
    if (preferences) {
      setFormData({
        eateries: {
          cuisineTypes: preferences.eateries.cuisineTypes || [],
          diningExperience: preferences.eateries.diningExperience || [],
          dietaryNeeds: preferences.eateries.dietaryNeeds || [],
        },
        shopping: {
          style: preferences.shopping.style || [],
          budget: preferences.shopping.budget || "mid-range",
          focusAreas: preferences.shopping.focusAreas || [],
        },
        entertainment: {
          venues: preferences.entertainment.venues || [],
          vibe: preferences.entertainment.vibe || [],
          interests: preferences.entertainment.interests || [],
        },
      })
    }
  }, [preferences])

  // Memoized handlers to prevent re-renders
  const handleEateriesChange = useCallback((field: string, value: string[] | string) => {
    setFormData((prev) => ({
      ...prev,
      eateries: { ...prev.eateries, [field]: value },
    }))
  }, [])

  const handleShoppingChange = useCallback((field: string, value: string[] | string) => {
    setFormData((prev) => ({
      ...prev,
      shopping: { ...prev.shopping, [field]: value },
    }))
  }, [])

  const handleEntertainmentChange = useCallback((field: string, value: string[] | string) => {
    setFormData((prev) => ({
      ...prev,
      entertainment: { ...prev.entertainment, [field]: value },
    }))
  }, [])

  // Memoized save handler
  const handleSave = useCallback(async () => {
    if (!isSubscribed) return

    try {
      const updateData: ActivityPreferencesUpdateData = {
        eateries: formData.eateries,
        shopping: formData.shopping,
        entertainment: formData.entertainment,
      }

      const success = await updatePreferences(updateData)

      if (success) {
        toast({
          title: "Preferences saved",
          description: "Your activity preferences have been updated successfully.",
        })
      } else {
        throw new Error("Failed to save preferences")
      }
    } catch (error) {
      console.error("Error saving activity preferences:", error)
      toast({
        title: "Error",
        description: "Failed to save your preferences. Please try again.",
        variant: "destructive",
      })
    }
  }, [isSubscribed, formData, updatePreferences, toast])

  // Show upgrade prompt for free users
  if (!isSubscribed) {
    return <UpgradePrompt />
  }

  // Show loading state
  if (preferencesLoading) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center py-8">
          <Loader2 className="h-6 w-6 animate-spin mr-2" />
          Loading preferences...
        </CardContent>
      </Card>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            Customize your Itinerary like a Pro
            <Badge variant="secondary" className="flex items-center gap-1">
              <Crown className="h-3 w-3" />
              Pro
            </Badge>
          </CardTitle>
          <CardDescription>
            Set your dining, shopping, and entertainment preferences to receive personalized
            itinerary suggestions tailored to your tastes.
          </CardDescription>
        </CardHeader>
      </Card>

      {/* Eateries Preferences */}
      <Card>
        <CardHeader>
          <CardTitle>Dining Preferences</CardTitle>
          <CardDescription>
            Choose your preferred cuisine types, dining experiences, and dietary needs.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <MultiSelect
            title="Cuisine Types"
            options={PREFERENCE_OPTIONS.eateries.cuisineTypes}
            selected={formData.eateries.cuisineTypes}
            onChange={(value) => handleEateriesChange("cuisineTypes", value)}
          />

          <MultiSelect
            title="Dining Experience"
            options={PREFERENCE_OPTIONS.eateries.diningExperience}
            selected={formData.eateries.diningExperience}
            onChange={(value) => handleEateriesChange("diningExperience", value)}
          />

          <MultiSelect
            title="Dietary Needs"
            options={PREFERENCE_OPTIONS.eateries.dietaryNeeds}
            selected={formData.eateries.dietaryNeeds}
            onChange={(value) => handleEateriesChange("dietaryNeeds", value)}
          />
        </CardContent>
      </Card>

      {/* Shopping Preferences */}
      <Card>
        <CardHeader>
          <CardTitle>Shopping Preferences</CardTitle>
          <CardDescription>
            Select your shopping style, budget range, and focus areas.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <MultiSelect
            title="Shopping Style"
            options={PREFERENCE_OPTIONS.shopping.style}
            selected={formData.shopping.style}
            onChange={(value) => handleShoppingChange("style", value)}
          />

          <div className="space-y-3">
            <h4 className="font-medium">Budget Range</h4>
            <RadioGroup
              value={formData.shopping.budget}
              onValueChange={(value) => handleShoppingChange("budget", value)}
            >
              {PREFERENCE_OPTIONS.shopping.budget.map((option) => (
                <div key={option} className="flex items-center space-x-2">
                  <RadioGroupItem value={option} id={`budget-${option}`} />
                  <Label htmlFor={`budget-${option}`}>
                    {option === "budget"
                      ? "Budget ($)"
                      : option === "mid-range"
                        ? "Mid-Range ($$)"
                        : "High-End ($$$)"}
                  </Label>
                </div>
              ))}
            </RadioGroup>
          </div>

          <MultiSelect
            title="Focus Areas"
            options={PREFERENCE_OPTIONS.shopping.focusAreas}
            selected={formData.shopping.focusAreas}
            onChange={(value) => handleShoppingChange("focusAreas", value)}
          />
        </CardContent>
      </Card>

      {/* Entertainment Preferences */}
      <Card>
        <CardHeader>
          <CardTitle>Entertainment Preferences</CardTitle>
          <CardDescription>
            Choose your preferred venues, vibes, and interests for entertainment activities.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <MultiSelect
            title="Venue Types"
            options={PREFERENCE_OPTIONS.entertainment.venues}
            selected={formData.entertainment.venues}
            onChange={(value) => handleEntertainmentChange("venues", value)}
          />

          <MultiSelect
            title="Preferred Vibe"
            options={PREFERENCE_OPTIONS.entertainment.vibe}
            selected={formData.entertainment.vibe}
            onChange={(value) => handleEntertainmentChange("vibe", value)}
          />

          <MultiSelect
            title="Interests"
            options={PREFERENCE_OPTIONS.entertainment.interests}
            selected={formData.entertainment.interests}
            onChange={(value) => handleEntertainmentChange("interests", value)}
          />
        </CardContent>
      </Card>

      {/* Save Button */}
      <div className="flex justify-end">
        <Button onClick={handleSave} disabled={saveLoading} className="min-w-[120px]">
          {saveLoading ? (
            <>
              <Loader2 className="h-4 w-4 animate-spin mr-2" />
              Saving...
            </>
          ) : (
            "Save Preferences"
          )}
        </Button>
      </div>
    </div>
  )
})

ActivityPreferencesSettings.displayName = "ActivityPreferencesSettings"
