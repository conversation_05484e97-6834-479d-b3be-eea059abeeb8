"use client"

import { useState, useEffect } from "react"
import Link from "next/link"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Calendar, Star, MessageSquare } from "lucide-react"
import { OptimizedImage } from "@/components/optimized-image"
import { formatDateRange } from "./utils"
import { getTripImageUrl } from "@/lib/utils/trip-image-utils"
import { Squad } from "@/lib/domains/squad/squad.types"
import { Trip } from "@/lib/domains/trip/trip.types"
import { TripReviewService } from "@/lib/domains/trip-review/trip-review.service"
import { TripReviewAggregate } from "@/lib/domains/trip-review/trip-review.types"
import { useUser } from "@/lib/domains/auth/auth.hooks"

interface PastTripsTabProps {
  squads: Squad[]
  pastTrips: Trip[]
  loading: boolean
}

interface TripWithReviewData extends Trip {
  reviewAggregate?: TripReviewAggregate
  userHasReviewed?: boolean
}

export function PastTripsTab({ squads, pastTrips, loading }: PastTripsTabProps) {
  const user = useUser()
  const [tripsWithReviews, setTripsWithReviews] = useState<TripWithReviewData[]>([])
  const [reviewsLoading, setReviewsLoading] = useState(true)

  // Load review data for all past trips with performance optimization
  useEffect(() => {
    const loadReviewData = async () => {
      if (!pastTrips.length || !user) {
        setTripsWithReviews(pastTrips)
        setReviewsLoading(false)
        return
      }

      try {
        setReviewsLoading(true)

        // Filter only completed trips to reduce unnecessary API calls
        const completedTrips = pastTrips.filter((trip) => trip.status === "completed")
        const nonCompletedTrips = pastTrips.filter((trip) => trip.status !== "completed")

        // Process completed trips with review data
        const completedTripsWithReviews = await Promise.all(
          completedTrips.map(async (trip) => {
            try {
              const [reviewAggregate, userReview] = await Promise.all([
                TripReviewService.getTripReviewAggregate(trip.id),
                TripReviewService.getUserReview(trip.id, user.uid),
              ])

              return {
                ...trip,
                reviewAggregate,
                userHasReviewed: userReview !== null,
              }
            } catch (error) {
              console.error(`Error loading review data for trip ${trip.id}:`, error)
              return { ...trip, reviewAggregate: undefined, userHasReviewed: false }
            }
          })
        )

        // Combine completed trips with review data and non-completed trips without review data
        const allTripsWithReviewData = [
          ...completedTripsWithReviews,
          ...nonCompletedTrips.map((trip) => ({
            ...trip,
            reviewAggregate: undefined,
            userHasReviewed: false,
          })),
        ]

        // Sort by original order (by date)
        const sortedTrips = allTripsWithReviewData.sort((a, b) => {
          const aIndex = pastTrips.findIndex((trip) => trip.id === a.id)
          const bIndex = pastTrips.findIndex((trip) => trip.id === b.id)
          return aIndex - bIndex
        })

        setTripsWithReviews(sortedTrips)
      } catch (error) {
        console.error("Error loading review data:", error)
        setTripsWithReviews(pastTrips)
      } finally {
        setReviewsLoading(false)
      }
    }

    loadReviewData()
  }, [pastTrips, user])
  const renderStarRating = (rating: number, totalReviews: number) => {
    return (
      <div className="flex items-center gap-1">
        <div className="flex">
          {[...Array(5)].map((_, i) => (
            <Star
              key={i}
              className={`h-4 w-4 ${
                i < Math.round(rating) ? "text-yellow-400 fill-yellow-400" : "text-muted-foreground"
              }`}
            />
          ))}
        </div>
        {totalReviews > 0 && (
          <span className="text-xs text-muted-foreground">
            {rating.toFixed(1)} ({totalReviews})
          </span>
        )}
      </div>
    )
  }

  return (
    <div className="space-y-4">
      <h2 className="text-xl font-semibold">Past Trips</h2>

      {tripsWithReviews.length > 0 ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {tripsWithReviews.map((trip) => {
            const isCompleted = trip.status === "completed"
            const hasReviewData = trip.reviewAggregate && trip.reviewAggregate.totalReviews > 0
            const needsReview = isCompleted && !trip.userHasReviewed

            return (
              <Card key={trip.id} className="h-full hover:shadow-md transition-shadow">
                <Link href={`/trips/${trip.id}`}>
                  <div className="aspect-video relative overflow-hidden rounded-t-lg">
                    <OptimizedImage
                      src={getTripImageUrl(trip, "400x200")}
                      alt={trip.destination}
                      aspectRatio="video"
                      className="rounded-t-lg"
                      priority
                    />
                    <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/60 to-transparent p-4">
                      <h3 className="text-white font-bold">{trip.destination}</h3>
                      <p className="text-white/80 text-sm">
                        {formatDateRange(trip.startDate, trip.endDate)}
                      </p>
                    </div>
                    {needsReview && (
                      <div className="absolute top-2 right-2">
                        <Badge className="bg-yellow-500 text-yellow-900 hover:bg-yellow-600">
                          Review Needed
                        </Badge>
                      </div>
                    )}
                  </div>
                </Link>
                <CardContent className="pt-4 space-y-3">
                  <div className="flex justify-between items-center">
                    <Badge>
                      {squads.find((s) => s.id === trip.squadId)?.name || "Unknown Squad"}
                    </Badge>
                    {hasReviewData ? (
                      renderStarRating(
                        trip.reviewAggregate!.averageRating,
                        trip.reviewAggregate!.totalReviews
                      )
                    ) : isCompleted ? (
                      <span className="text-xs text-muted-foreground">No reviews yet</span>
                    ) : (
                      <div className="flex">
                        {[...Array(5)].map((_, i) => (
                          <Star key={i} className="h-4 w-4 text-muted-foreground" />
                        ))}
                      </div>
                    )}
                  </div>
                  <p className="text-sm text-muted-foreground">
                    {trip.description || "No description available"}
                  </p>

                  {/* Review Actions for Completed Trips */}
                  {isCompleted && (
                    <div className="flex gap-2 pt-2">
                      {needsReview ? (
                        <Link href={`/trips/${trip.id}/review`} className="flex-1">
                          <Button size="sm" className="w-full">
                            <Star className="h-3 w-3 mr-1" />
                            Review Trip
                          </Button>
                        </Link>
                      ) : (
                        <Link href={`/trips/${trip.id}/review`} className="flex-1">
                          <Button variant="outline" size="sm" className="w-full">
                            <Star className="h-3 w-3 mr-1" />
                            View Review
                          </Button>
                        </Link>
                      )}
                      <Link href={`/trips/${trip.id}?tab=chat`}>
                        <Button variant="outline" size="sm">
                          <MessageSquare className="h-3 w-3" />
                        </Button>
                      </Link>
                    </div>
                  )}
                </CardContent>
              </Card>
            )
          })}
        </div>
      ) : loading || reviewsLoading ? (
        <Card>
          <CardContent className="flex flex-col items-center justify-center p-6">
            <div className="w-12 h-12 rounded-full bg-primary/10 flex items-center justify-center mb-4">
              <Calendar className="h-6 w-6 text-primary animate-pulse" />
            </div>
            <p className="font-medium text-center">Loading Past Trips...</p>
            <p className="text-sm text-muted-foreground text-center mt-1">
              Please wait while we load your trip history
            </p>
          </CardContent>
        </Card>
      ) : (
        <Card>
          <CardContent className="flex flex-col items-center justify-center p-6">
            <div className="w-12 h-12 rounded-full bg-primary/10 flex items-center justify-center mb-4">
              <Calendar className="h-6 w-6 text-primary" />
            </div>
            <p className="font-medium text-center">No Past Trips</p>
            <p className="text-sm text-muted-foreground text-center mt-1">
              Your completed trips will appear here
            </p>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
