"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>rigger } from "@/components/ui/tabs"
import { useToast } from "@/components/ui/use-toast"
import { Plus, Info } from "lucide-react"
import { Trip } from "@/lib/domains/trip/trip.types"
import { Task } from "@/lib/domains/task/task.types"
import { User } from "@/lib/domains/user/user.types"
import { UserTrip } from "@/lib/domains/user-trip/user-trip.types"
import { useTaskStore } from "@/lib/domains/task/task.store"
import {
  useCreateTask,
  useUpdateTask,
  useDeleteTask,
  useToggleTaskCompletion,
} from "@/lib/domains/task/task.hooks"
import { TaskSuggestions } from "./task-suggestions"
import { usePagination } from "@/hooks/use-pagination"
import { Pa<PERSON>ationControl } from "@/components/pagination-control"
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Toolt<PERSON>Trigger } from "@/components/ui/tooltip"
import { TaskDialogs } from "./task-dialogs"
import { TaskListItem } from "./task-list-item"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"

interface TasksTabProps {
  trip: Trip
  tasks: Task[]
  currentUserId: string
  attendeesWithDetails: (UserTrip & { user: User })[]
  isActive?: boolean
}

export function TasksTab({
  trip,
  tasks: initialTasks,
  currentUserId,
  attendeesWithDetails,
  isActive = true,
}: TasksTabProps) {
  const { toast } = useToast()
  const { setTasks } = useTaskStore()
  const { createTask, loading: createLoading } = useCreateTask()
  const { updateTask, loading: updateLoading } = useUpdateTask()
  const { deleteTask, loading: deleteLoading } = useDeleteTask()
  const { toggleTaskCompletion } = useToggleTaskCompletion()

  const [updatingTaskId, setUpdatingTaskId] = useState<string | null>(null)
  const [tasks, setLocalTasks] = useState<Task[]>(initialTasks)
  const [activeTab, setActiveTab] = useState("my-tasks")

  // Filter states
  const [categoryFilter, setCategoryFilter] = useState<string>("all")
  const [assigneeFilter, setAssigneeFilter] = useState<string>("all")
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false)
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false)
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false)
  const [editingTask, setEditingTask] = useState<Task | null>(null)
  const [taskToDelete, setTaskToDelete] = useState<Task | null>(null)

  // Get trip attendees with user details - REMOVED: This is already fetched in parent component
  // const { attendeesWithDetails } = useRealtimeTripAttendeesWithDetails(trip.id)

  // Update local state when props change
  useEffect(() => {
    setLocalTasks(initialTasks)
    setTasks(initialTasks)
  }, [initialTasks, setTasks])

  // Function to determine if a task is a priority task (flight, accommodation, transportation)
  const isPriorityTask = (task: Task) => {
    const title = task.title.toLowerCase()
    return (
      title.includes("flight") ||
      title.includes("accommodation") ||
      title.includes("hotel") ||
      title.includes("transportation") ||
      title.includes("car rental")
    )
  }

  // Sort tasks with current user's tasks first, then priority tasks, then by completion status
  const sortTasks = (taskList: (Task & { assignee?: User })[]) => {
    return [...taskList].sort((a, b) => {
      // First sort by current user's tasks
      const aIsCurrentUser = a.assigneeId === currentUserId
      const bIsCurrentUser = b.assigneeId === currentUserId

      if (aIsCurrentUser && !bIsCurrentUser) return -1
      if (!aIsCurrentUser && bIsCurrentUser) return 1

      // Then sort by priority
      const aPriority = isPriorityTask(a)
      const bPriority = isPriorityTask(b)

      if (aPriority && !bPriority) return -1
      if (!aPriority && bPriority) return 1

      // Then sort by completion status
      if (!a.completed && b.completed) return -1
      if (a.completed && !b.completed) return 1

      return 0
    })
  }

  // Apply filters to tasks
  const applyFilters = (taskList: Task[]) => {
    return taskList.filter((task) => {
      // Category filter
      if (categoryFilter !== "all" && task.category !== categoryFilter) {
        return false
      }

      // Assignee filter
      if (assigneeFilter !== "all" && task.assigneeId !== assigneeFilter) {
        return false
      }

      return true
    })
  }

  // Filter tasks
  const filteredTasks = applyFilters(tasks)
  const completedTasks = filteredTasks.filter((task) => task.completed)
  const myTasks = filteredTasks.filter((task) => task.assigneeId === currentUserId)

  // Sort all task lists
  const sortedTasks = sortTasks(filteredTasks)
  const sortedMyTasks = sortTasks(myTasks)
  const sortedCompletedTasks = sortTasks(completedTasks)

  // Handle task creation
  const handleAddTask = () => {
    setIsAddDialogOpen(true)
  }

  // Handle task edit
  const handleEditTask = (task: Task) => {
    setEditingTask(task)
    setIsEditDialogOpen(true)
  }

  // Handle task deletion
  const handleDeleteTask = (task: Task) => {
    setTaskToDelete(task)
    setIsDeleteDialogOpen(true)
  }

  // Handle task creation submission
  const handleTaskCreated = async (taskData: any) => {
    // Add the tripId to the task data
    const taskDataWithTripId = {
      ...taskData,
      tripId: trip.id,
    }

    // Use the createTask hook to create the task
    const success = await createTask(taskDataWithTripId)

    if (success) {
      setIsAddDialogOpen(false)
    }

    return success
  }

  // Handle task update submission
  const handleTaskUpdated = async (): Promise<boolean> => {
    if (!editingTask) return false

    // If user is not a leader, ensure they don't change the assignee
    if (!isLeader) {
      // Get the original task from the tasks array
      const originalTask = tasks.find((task) => task.id === editingTask.id)
      if (originalTask && originalTask.assigneeId !== editingTask.assigneeId) {
        // Reset the assignee to the original value
        editingTask.assigneeId = originalTask.assigneeId
        toast({
          title: "Permission denied",
          description: "Only the trip leader can reassign tasks",
          variant: "destructive",
        })
        // Continue with the update using the original assignee
      }
    }

    // Always update all fields to ensure changes are applied
    const updateData: Partial<Task> = {
      title: editingTask.title,
      description: editingTask.description,
      category: editingTask.category,
      assigneeId: editingTask.assigneeId,
      dueDate: editingTask.dueDate,
    }

    // Use the updateTask hook to update the task
    const success = await updateTask(editingTask.id, updateData)

    if (success) {
      setIsEditDialogOpen(false)
      setEditingTask(null) // Clear the editing task
    }

    return success
  }

  // Handle task deletion submission
  const handleTaskDeleted = async (): Promise<boolean> => {
    if (!taskToDelete) return false

    // Use the deleteTask hook to delete the task
    const success = await deleteTask(taskToDelete.id)

    if (success) {
      setIsDeleteDialogOpen(false)
    }

    return success
  }

  const handleToggleTaskCompletion = async (
    taskId: string,
    completed: boolean,
    assigneeId: string
  ) => {
    try {
      setUpdatingTaskId(taskId)

      // Optimistic UI update
      const newCompleted = !completed
      setLocalTasks((prevTasks) => {
        return prevTasks.map((task) =>
          task.id === taskId ? { ...task, completed: newCompleted } : task
        )
      })

      // Use the toggleTaskCompletion hook to toggle the task completion
      const isLeader = trip.leaderId === currentUserId || trip.createdBy === currentUserId
      const success = await toggleTaskCompletion(taskId, completed, trip.id, isLeader, assigneeId)

      if (!success) {
        // Revert optimistic update if failed
        setLocalTasks((prevTasks) => {
          return prevTasks.map((task) => (task.id === taskId ? { ...task, completed } : task))
        })
      }
    } catch (error) {
      console.error("Error updating task:", error)

      // Revert optimistic update on error
      setLocalTasks((prevTasks) => {
        return prevTasks.map((task) => (task.id === taskId ? { ...task, completed } : task))
      })
    } finally {
      setUpdatingTaskId(null)
    }
  }

  const renderTaskList = (taskList: Task[], currentPage: number, itemsPerPage: number) => {
    if (taskList.length === 0) {
      return (
        <Card>
          <CardContent className="flex flex-col items-center justify-center p-6">
            <p className="font-medium text-center">No tasks found</p>
            <p className="text-sm text-muted-foreground text-center mt-1 mb-4">
              {tasks.length === 0
                ? "Start by adding tasks to your trip"
                : "No tasks match the current filter"}
            </p>
            <Button onClick={handleAddTask}>
              <Plus className="mr-2 h-4 w-4" /> Add Task
            </Button>
          </CardContent>
        </Card>
      )
    }

    // Get paginated items
    const startIndex = (currentPage - 1) * itemsPerPage
    const paginatedTasks = taskList.slice(startIndex, startIndex + itemsPerPage)

    return (
      <div className="space-y-2">
        {paginatedTasks.map((task) => {
          // Check if user can complete this task
          const isTaskOwner = task.assigneeId === currentUserId
          const isLeader = trip.leaderId === currentUserId || trip.createdBy === currentUserId

          // Find assignee details from attendees
          const assigneeDetails = attendeesWithDetails.find(
            (a) => a.user.uid === task.assigneeId
          )?.user

          return (
            <TaskListItem
              key={task.id}
              task={{
                ...task,
                assignee: assigneeDetails,
              }}
              isTaskOwner={isTaskOwner}
              isLeader={isLeader}
              onComplete={(taskId, completed) =>
                handleToggleTaskCompletion(taskId, completed, task.assigneeId)
              }
              onEdit={() => handleEditTask(task)}
              onDelete={() => handleDeleteTask(task)}
              isUpdating={updatingTaskId === task.id}
              isTripCompleted={isTripCompleted}
            />
          )
        })}
      </div>
    )
  }

  // Function to handle task added from suggestions
  const handleTaskAdded = () => {
    // The real-time listener will automatically update the tasks list
    // This is just a callback to notify the parent component that a task was added
    // We don't need to do anything here as our query-based listener will catch the new task
  }

  // Pagination for all tasks
  const allTasksPagination = usePagination({ totalItems: sortedTasks.length, itemsPerPage: 10 })

  // Pagination for my tasks
  const myTasksPagination = usePagination({ totalItems: sortedMyTasks.length, itemsPerPage: 10 })

  // Pagination for completed tasks
  const completedTasksPagination = usePagination({
    totalItems: sortedCompletedTasks.length,
    itemsPerPage: 10,
  })

  // Reset pagination when tab changes
  useEffect(() => {
    switch (activeTab) {
      case "my-tasks":
        myTasksPagination.goToPage(1)
        break
      case "completed":
        completedTasksPagination.goToPage(1)
        break
      default:
        allTasksPagination.goToPage(1)
        break
    }
  }, [activeTab])

  // Check if user is trip leader
  const isLeader = trip.leaderId === currentUserId || trip.createdBy === currentUserId

  // Check if trip is completed
  const isTripCompleted = trip.status === "completed"

  return (
    <div className="space-y-3 md:space-y-4">
      {/* Show completion notice for completed trips */}
      {isTripCompleted && (
        <div className="bg-green-50 border border-green-200 rounded-lg p-4">
          <div className="flex items-center gap-2">
            <span className="text-green-600 font-medium">✅ Trip Completed</span>
          </div>
          <p className="text-green-700 text-sm mt-1">
            This trip has been completed. Tasks are now read-only for historical reference.
          </p>
        </div>
      )}

      <div className="flex flex-wrap md:flex-nowrap justify-between items-center gap-2">
        <div className="flex items-center gap-2">
          <h2 className="text-lg md:text-xl font-semibold">Trip Tasks</h2>
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button variant="ghost" size="icon" className="h-6 w-6">
                  <Info className="h-4 w-4" />
                </Button>
              </TooltipTrigger>
              <TooltipContent className="max-w-xs">
                <p>
                  {isTripCompleted
                    ? "Tasks are read-only for completed trips."
                    : "Only the task owner or trip leader can complete tasks."}
                </p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        </div>
        {!isTripCompleted && (
          <Button onClick={handleAddTask}>
            <Plus className="mr-2 h-4 w-4" /> Add Task
          </Button>
        )}
      </div>

      {/* Filter controls */}
      <div className="flex flex-wrap gap-2 mt-2">
        <Select value={categoryFilter} onValueChange={setCategoryFilter}>
          <SelectTrigger className="w-[180px]">
            <SelectValue placeholder="Filter by category" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Categories</SelectItem>
            <SelectItem value="planning">Planning</SelectItem>
            <SelectItem value="booking">Booking</SelectItem>
            <SelectItem value="preparation">Preparation</SelectItem>
            <SelectItem value="coordination">Coordination</SelectItem>
            <SelectItem value="during-trip">During Trip</SelectItem>
          </SelectContent>
        </Select>

        <Select value={assigneeFilter} onValueChange={setAssigneeFilter}>
          <SelectTrigger className="w-[180px]">
            <SelectValue placeholder="Filter by assignee" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Assignees</SelectItem>
            <SelectItem value={currentUserId}>My Tasks</SelectItem>
            {attendeesWithDetails.map((attendee) => (
              <SelectItem key={attendee.user.uid} value={attendee.user.uid}>
                {attendee.user.displayName || attendee.user.email}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      <Tabs
        defaultValue="my-tasks"
        onValueChange={setActiveTab}
        value={activeTab}
        className="w-full"
      >
        <TabsList className="w-full md:w-auto">
          <TabsTrigger value="my-tasks" className="flex-1 md:flex-initial">
            My Tasks
          </TabsTrigger>
          <TabsTrigger value="all" className="flex-1 md:flex-initial">
            All Tasks
          </TabsTrigger>
          <TabsTrigger value="completed" className="flex-1 md:flex-initial">
            Completed
          </TabsTrigger>
        </TabsList>

        <TabsContent value="all" className="mt-3 md:mt-4 w-full max-w-full overflow-x-hidden">
          {renderTaskList(
            sortedTasks,
            allTasksPagination.currentPage,
            allTasksPagination.itemsPerPage
          )}
          <PaginationControl
            currentPage={allTasksPagination.currentPage}
            totalPages={allTasksPagination.totalPages}
            onPageChange={allTasksPagination.goToPage}
          />
          {/* Show task suggestions if there are tasks to display and tab is active */}
          {isActive && (
            <TaskSuggestions
              trip={trip}
              tasks={tasks}
              currentUserId={currentUserId}
              onTaskAdded={handleTaskAdded}
              isLeader={isLeader}
            />
          )}
        </TabsContent>

        <TabsContent value="my-tasks" className="mt-3 md:mt-4 w-full max-w-full overflow-x-hidden">
          {renderTaskList(
            sortedMyTasks,
            myTasksPagination.currentPage,
            myTasksPagination.itemsPerPage
          )}
          <PaginationControl
            currentPage={myTasksPagination.currentPage}
            totalPages={myTasksPagination.totalPages}
            onPageChange={myTasksPagination.goToPage}
          />
          {isActive && (
            <TaskSuggestions
              trip={trip}
              tasks={tasks}
              currentUserId={currentUserId}
              onTaskAdded={handleTaskAdded}
              isLeader={isLeader}
            />
          )}
        </TabsContent>

        <TabsContent value="completed" className="mt-3 md:mt-4 w-full max-w-full overflow-x-hidden">
          {renderTaskList(
            sortedCompletedTasks,
            completedTasksPagination.currentPage,
            completedTasksPagination.itemsPerPage
          )}
          <PaginationControl
            currentPage={completedTasksPagination.currentPage}
            totalPages={completedTasksPagination.totalPages}
            onPageChange={completedTasksPagination.goToPage}
          />
        </TabsContent>
      </Tabs>

      {/* Task Dialogs for Add/Edit/Delete */}
      <TaskDialogs
        editingTask={editingTask}
        isEditDialogOpen={isEditDialogOpen}
        setIsEditDialogOpen={setIsEditDialogOpen}
        isDeleteDialogOpen={isDeleteDialogOpen}
        setIsDeleteDialogOpen={setIsDeleteDialogOpen}
        isAddDialogOpen={isAddDialogOpen}
        setIsAddDialogOpen={setIsAddDialogOpen}
        taskToDelete={taskToDelete}
        squadMembers={attendeesWithDetails.map((a) => a.user)}
        onUpdateTask={handleTaskUpdated}
        onDeleteTask={handleTaskDeleted}
        onAddTask={handleTaskCreated}
        isLeader={isLeader}
        isCreating={createLoading}
        isUpdating={updateLoading}
        isDeleting={deleteLoading}
      />
    </div>
  )
}
