"use server"

import { put } from "@vercel/blob"
import { redirect } from "next/navigation"

// Maximum file size: 1MB (server action limit)
const MAX_FILE_SIZE = 1 * 1024 * 1024

// Allowed file types
const ALLOWED_TYPES = ["image/jpeg", "image/jpg", "image/png", "image/webp"]

export interface UploadTravelDetailsResult {
  success: boolean
  url?: string
  error?: string
}

export async function uploadTravelDetailsAction(
  tripId: string,
  userId: string,
  type: "flight" | "accommodation",
  formData: FormData
): Promise<UploadTravelDetailsResult> {
  console.log("Upload Travel Details Action:", { tripId, userId, type })

  try {
    // Validate user ID is provided (authentication should be handled by the calling component)
    if (!userId) {
      console.log("No userId provided, redirecting to login")
      redirect("/login")
    }

    const file = formData.get("file") as File
    console.log("File:", { name: file?.name, size: file?.size, type: file?.type })

    // Validate required fields
    if (!file) {
      console.log("Validation failed: No file provided")
      return {
        success: false,
        error: "No file provided",
      }
    }

    if (!tripId) {
      console.log("Validation failed: No tripId provided")
      return {
        success: false,
        error: "Trip ID is required",
      }
    }

    if (!type || !["flight", "accommodation"].includes(type)) {
      console.log("Validation failed: Invalid type:", type)
      return {
        success: false,
        error: "Type must be either 'flight' or 'accommodation'",
      }
    }

    // Validate file type
    if (!ALLOWED_TYPES.includes(file.type)) {
      console.log("Validation failed: Invalid file type:", file.type)
      return {
        success: false,
        error: "Invalid file type. Only JPEG, PNG, and WebP images are allowed.",
      }
    }

    // Validate file size
    if (file.size > MAX_FILE_SIZE) {
      console.log("Validation failed: File too large:", file.size)
      return {
        success: false,
        error: "File size too large. Maximum size is 1MB.",
      }
    }

    // Check if Vercel Blob token is available
    if (!process.env.BLOB_READ_WRITE_TOKEN) {
      console.error("BLOB_READ_WRITE_TOKEN is not configured")
      return {
        success: false,
        error: "Storage service is not configured",
      }
    }

    // Generate a unique filename
    const timestamp = Date.now()
    const fileExtension = file.name.split(".").pop() || "jpg"
    const filename = `travel-details/${tripId}/${userId}/${type}-${timestamp}.${fileExtension}`
    console.log("Generated filename:", filename)

    try {
      console.log("Starting Vercel Blob upload...")

      // Upload to Vercel Blob
      const blob = await put(filename, file, {
        access: "public",
        token: process.env.BLOB_READ_WRITE_TOKEN,
      })

      console.log("Vercel Blob upload successful:", blob.url)

      return {
        success: true,
        url: blob.url,
      }
    } catch (uploadError) {
      console.error("Vercel Blob upload error details:", {
        error: uploadError,
        message: uploadError instanceof Error ? uploadError.message : "Unknown error",
        stack: uploadError instanceof Error ? uploadError.stack : undefined,
        filename,
        fileSize: file.size,
        fileType: file.type,
      })

      // Provide more specific error messages based on the error type
      let errorMessage = "Failed to upload image to storage"
      if (uploadError instanceof Error) {
        if (uploadError.message.includes("token")) {
          errorMessage = "Storage authentication failed"
        } else if (uploadError.message.includes("size")) {
          errorMessage = "File size exceeds storage limits"
        } else if (
          uploadError.message.includes("network") ||
          uploadError.message.includes("fetch")
        ) {
          errorMessage = "Network error during upload"
        }
      }

      return {
        success: false,
        error: errorMessage,
      }
    }
  } catch (error) {
    console.error("Travel details upload error details:", {
      error,
      message: error instanceof Error ? error.message : "Unknown error",
      stack: error instanceof Error ? error.stack : undefined,
      tripId,
      userId,
      type,
    })

    return {
      success: false,
      error: "Internal server error",
    }
  }
}
