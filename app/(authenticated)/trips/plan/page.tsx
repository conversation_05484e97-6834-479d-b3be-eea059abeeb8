"use client"

import { useState } from "react"
import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import {
  ArrowLeft,
  Calendar,
  DollarSign,
  Check,
  ArrowRight,
  Sparkles,
  Users,
  MapPin,
  Plane,
  Hotel,
  Utensils,
} from "lucide-react"
import { AppHeader } from "@/components/app-header"
import { AppSidebar } from "@/components/app-sidebar"
import { Checkbox } from "@/components/ui/checkbox"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { MonthSelector } from "@/components/month-selector"

export default function PlanTripPage() {
  const [step, setStep] = useState(1)
  const [selectedSquad, setSelectedSquad] = useState<string | null>(null)
  const [selectedIdea, setSelectedIdea] = useState<number | null>(null)
  const [selectedMonths, setSelectedMonths] = useState<string[]>([])
  const totalSteps = 4

  const nextStep = () => {
    if (step < totalSteps) {
      setStep(step + 1)
    }
  }

  const prevStep = () => {
    if (step > 1) {
      setStep(step - 1)
    }
  }

  return (
    <div className="min-h-screen flex flex-col">
      <AppHeader />

      <div className="flex-1 flex">
        <AppSidebar />

        <main className="flex-1 p-6">
          <div className="mb-6 flex items-center">
            <Link href="/dashboard" className="mr-4">
              <Button variant="ghost" size="icon">
                <ArrowLeft className="h-5 w-5" />
              </Button>
            </Link>
            <div>
              <h1 className="text-3xl font-bold">Plan a Trip</h1>
              <p className="text-muted-foreground">Create a new adventure for your squad</p>
            </div>
          </div>

          <div className="mb-8">
            <div className="flex items-center">
              <div className="flex items-center justify-center w-8 h-8 rounded-full bg-primary text-primary-foreground font-medium">
                1
              </div>
              <div className={`h-1 w-16 mx-2 ${step >= 2 ? "bg-primary" : "bg-muted"}`}></div>
              <div
                className={`flex items-center justify-center w-8 h-8 rounded-full ${step >= 2 ? "bg-primary text-primary-foreground" : "bg-muted text-muted-foreground"} font-medium`}
              >
                2
              </div>
              <div className={`h-1 w-16 mx-2 ${step >= 3 ? "bg-primary" : "bg-muted"}`}></div>
              <div
                className={`flex items-center justify-center w-8 h-8 rounded-full ${step >= 3 ? "bg-primary text-primary-foreground" : "bg-muted text-muted-foreground"} font-medium`}
              >
                3
              </div>
              <div className={`h-1 w-16 mx-2 ${step >= 4 ? "bg-primary" : "bg-muted"}`}></div>
              <div
                className={`flex items-center justify-center w-8 h-8 rounded-full ${step >= 4 ? "bg-primary text-primary-foreground" : "bg-muted text-muted-foreground"} font-medium`}
              >
                4
              </div>
            </div>
            <div className="flex mt-2">
              <div className="w-8 text-xs text-center">Squad</div>
              <div className="flex-1"></div>
              <div className="w-8 text-xs text-center">Prefs</div>
              <div className="flex-1"></div>
              <div className="w-8 text-xs text-center">Ideas</div>
              <div className="flex-1"></div>
              <div className="w-8 text-xs text-center">Review</div>
            </div>
          </div>

          {step === 1 && (
            <div className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>Select a Squad</CardTitle>
                  <CardDescription>
                    Choose which squad you're planning this trip for
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    {squads.map((squad) => (
                      <div
                        key={squad.id}
                        className={`rounded-lg border p-4 cursor-pointer transition-all ${selectedSquad === squad.id ? "border-primary bg-primary/5 ring-2 ring-primary/20" : "hover:border-primary/50"}`}
                        onClick={() => setSelectedSquad(squad.id)}
                      >
                        <div className="flex justify-between items-start mb-2">
                          <h3 className="font-medium">{squad.name}</h3>
                          {selectedSquad === squad.id && (
                            <div className="h-5 w-5 rounded-full bg-primary flex items-center justify-center">
                              <Check className="h-3 w-3 text-white" />
                            </div>
                          )}
                        </div>
                        <p className="text-sm text-muted-foreground mb-3">
                          {squad.members?.length || squad.memberCount || 0} members
                        </p>
                        <div className="flex -space-x-2 overflow-hidden">
                          {(squad.members || []).slice(0, 4).map((member, i) => (
                            <Avatar key={i} className="border-2 border-background h-8 w-8">
                              <AvatarImage src={member.avatar} alt={member.name} />
                              <AvatarFallback>
                                {member.name?.charAt(0) || (i + 1).toString()}
                              </AvatarFallback>
                            </Avatar>
                          ))}
                          {(squad.members?.length || squad.memberCount || 0) > 4 && (
                            <div className="flex items-center justify-center w-8 h-8 rounded-full border-2 border-background bg-muted text-xs">
                              +{(squad.members?.length || squad.memberCount || 0) - 4}
                            </div>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
                <CardFooter className="flex justify-end">
                  <Button onClick={nextStep} disabled={!selectedSquad}>
                    Continue <ArrowRight className="ml-2 h-4 w-4" />
                  </Button>
                </CardFooter>
              </Card>
            </div>
          )}

          {step === 2 && (
            <div className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>Trip Preferences</CardTitle>
                  <CardDescription>Tell us what you're looking for in this trip</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-6">
                    <div className="space-y-3">
                      <Label>Trip Type</Label>
                      <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-3">
                        {tripTypes.map((type) => (
                          <div key={type.id} className="relative">
                            <input
                              type="checkbox"
                              id={`trip-type-${type.id}`}
                              className="peer sr-only"
                            />
                            <label
                              htmlFor={`trip-type-${type.id}`}
                              className="flex flex-col items-center gap-2 rounded-lg border-2 border-muted bg-popover p-4 hover:bg-accent hover:text-accent-foreground peer-checked:border-primary peer-checked:bg-primary/5 cursor-pointer"
                            >
                              {type.icon}
                              <span className="text-sm font-medium">{type.name}</span>
                            </label>
                          </div>
                        ))}
                      </div>
                    </div>

                    <div className="space-y-3">
                      <Label>When do you want to travel?</Label>
                      <MonthSelector selectedMonths={selectedMonths} onChange={setSelectedMonths} />
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="space-y-3">
                        <Label htmlFor="trip-duration">Trip Duration</Label>
                        <Select defaultValue="3-5">
                          <SelectTrigger id="trip-duration">
                            <SelectValue placeholder="Select duration" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="weekend">Weekend (2-3 days)</SelectItem>
                            <SelectItem value="3-5">Short Trip (3-5 days)</SelectItem>
                            <SelectItem value="week">Week-long (6-8 days)</SelectItem>
                            <SelectItem value="extended">Extended (9-14 days)</SelectItem>
                            <SelectItem value="long">Long Trip (15+ days)</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>

                      <div className="space-y-3">
                        <Label htmlFor="trip-budget">Budget per Person</Label>
                        <Select defaultValue="mid">
                          <SelectTrigger id="trip-budget">
                            <SelectValue placeholder="Select budget" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="budget">Budget ($500 - $800)</SelectItem>
                            <SelectItem value="mid">Mid-range ($800 - $1,200)</SelectItem>
                            <SelectItem value="premium">Premium ($1,200 - $2,000)</SelectItem>
                            <SelectItem value="luxury">Luxury ($2,000+)</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    </div>

                    <div className="space-y-3">
                      <Label>Trip Activities</Label>
                      <div className="grid grid-cols-2 sm:grid-cols-3 gap-2">
                        {activities.map((activity) => (
                          <div key={activity} className="flex items-center space-x-2">
                            <Checkbox id={`activity-${activity}`} />
                            <label
                              htmlFor={`activity-${activity}`}
                              className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                            >
                              {activity}
                            </label>
                          </div>
                        ))}
                      </div>
                    </div>

                    <div className="space-y-3">
                      <Label htmlFor="special-requests">Special Requests (Optional)</Label>
                      <textarea
                        id="special-requests"
                        rows={3}
                        placeholder="Any specific requirements or preferences for this trip..."
                        className="flex w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                      ></textarea>
                    </div>
                  </div>
                </CardContent>
                <CardFooter className="flex justify-between">
                  <Button variant="outline" onClick={prevStep}>
                    <ArrowLeft className="mr-2 h-4 w-4" /> Back
                  </Button>
                  <Button onClick={nextStep}>
                    Continue <ArrowRight className="ml-2 h-4 w-4" />
                  </Button>
                </CardFooter>
              </Card>
            </div>
          )}

          {step === 3 && (
            <div className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>Trip Ideas</CardTitle>
                  <CardDescription>
                    Choose from AI recommendations or create your own trip
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <Tabs defaultValue="recommendations">
                    <TabsList className="mb-4">
                      <TabsTrigger value="recommendations">AI Recommendations</TabsTrigger>
                      <TabsTrigger value="custom">Custom Trip</TabsTrigger>
                    </TabsList>

                    <TabsContent value="recommendations">
                      <div className="space-y-4">
                        <div className="flex items-center gap-2 p-3 rounded-md bg-primary/10 border border-primary/20">
                          <Sparkles className="h-5 w-5 text-primary" />
                          <p className="text-sm">
                            Based on your squad's preferences, we've generated these trip ideas for
                            you.
                          </p>
                        </div>

                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          {tripIdeas.map((idea, index) => (
                            <div
                              key={index}
                              className={`rounded-lg border overflow-hidden cursor-pointer transition-all ${selectedIdea === index ? "ring-2 ring-primary border-primary" : "hover:border-primary/50"}`}
                              onClick={() => setSelectedIdea(index)}
                            >
                              <div className="aspect-video relative">
                                <img
                                  src={idea.image || "/placeholder.svg?height=150&width=300"}
                                  alt={idea.destination}
                                  className="object-cover w-full h-full"
                                />
                                <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/60 to-transparent p-3">
                                  <h3 className="text-white font-bold">{idea.destination}</h3>
                                </div>
                                {selectedIdea === index && (
                                  <div className="absolute top-2 right-2 h-6 w-6 rounded-full bg-primary flex items-center justify-center">
                                    <Check className="h-4 w-4 text-white" />
                                  </div>
                                )}
                              </div>
                              <div className="p-3 space-y-2">
                                <div className="flex gap-2 flex-wrap">
                                  {idea.tags.map((tag, i) => (
                                    <Badge key={i} variant="secondary" className="text-xs">
                                      {tag}
                                    </Badge>
                                  ))}
                                </div>
                                <p className="text-sm text-muted-foreground line-clamp-2">
                                  {idea.description}
                                </p>
                                <div className="flex justify-between items-center pt-2">
                                  <div className="flex items-center gap-1 text-sm">
                                    <DollarSign className="h-3 w-3" />
                                    <span>{idea.budget}</span>
                                  </div>
                                  <div className="flex items-center gap-1 text-sm">
                                    <Calendar className="h-3 w-3" />
                                    <span>{idea.duration}</span>
                                  </div>
                                </div>
                              </div>
                            </div>
                          ))}
                        </div>
                      </div>
                    </TabsContent>

                    <TabsContent value="custom">
                      <div className="space-y-4">
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <div className="space-y-2">
                            <Label htmlFor="destination">Destination</Label>
                            <Input
                              id="destination"
                              placeholder="e.g., Miami Beach, Yosemite National Park"
                            />
                          </div>
                          <div className="space-y-2">
                            <Label htmlFor="dates">Dates</Label>
                            <Input id="dates" placeholder="e.g., Aug 15-20, 2023" />
                          </div>
                          <div className="space-y-2">
                            <Label htmlFor="budget">Budget per Person</Label>
                            <select
                              id="budget"
                              className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                            >
                              <option>$500 - $800 (Budget)</option>
                              <option>$800 - $1,200 (Mid-range)</option>
                              <option>$1,200 - $2,000 (Premium)</option>
                              <option>$2,000+ (Luxury)</option>
                            </select>
                          </div>
                          <div className="space-y-2">
                            <Label htmlFor="trip-type">Trip Type</Label>
                            <select
                              id="trip-type"
                              className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                            >
                              <option>Beach Vacation</option>
                              <option>City Break</option>
                              <option>Adventure Trip</option>
                              <option>Nature Retreat</option>
                              <option>Road Trip</option>
                              <option>Other</option>
                            </select>
                          </div>
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor="description">Trip Description</Label>
                          <textarea
                            id="description"
                            rows={3}
                            placeholder="Describe your trip idea..."
                            className="flex w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                          ></textarea>
                        </div>
                      </div>
                    </TabsContent>
                  </Tabs>
                </CardContent>
                <CardFooter className="flex justify-between">
                  <Button variant="outline" onClick={prevStep}>
                    <ArrowLeft className="mr-2 h-4 w-4" /> Back
                  </Button>
                  <Button onClick={nextStep} disabled={selectedIdea === null}>
                    Continue <ArrowRight className="ml-2 h-4 w-4" />
                  </Button>
                </CardFooter>
              </Card>
            </div>
          )}

          {step === 4 && selectedIdea !== null && (
            <div className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>Review & Confirm</CardTitle>
                  <CardDescription>Review your trip details before creating</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <div className="aspect-video rounded-lg overflow-hidden mb-4">
                        <img
                          src={
                            tripIdeas[selectedIdea].image || "/placeholder.svg?height=300&width=500"
                          }
                          alt={tripIdeas[selectedIdea].destination}
                          className="object-cover w-full h-full"
                        />
                      </div>

                      <div className="space-y-4">
                        <div>
                          <h3 className="text-lg font-semibold">
                            {tripIdeas[selectedIdea].destination}
                          </h3>
                          <div className="flex gap-2 mt-1 flex-wrap">
                            {tripIdeas[selectedIdea].tags.map((tag, i) => (
                              <Badge key={i} variant="secondary">
                                {tag}
                              </Badge>
                            ))}
                          </div>
                        </div>

                        <p className="text-muted-foreground">
                          {tripIdeas[selectedIdea].description}
                        </p>

                        <div className="grid grid-cols-2 gap-4">
                          <div className="space-y-1">
                            <p className="text-sm text-muted-foreground">Duration</p>
                            <div className="flex items-center gap-2">
                              <Calendar className="h-4 w-4 text-primary" />
                              <p className="font-medium">{tripIdeas[selectedIdea].duration}</p>
                            </div>
                          </div>
                          <div className="space-y-1">
                            <p className="text-sm text-muted-foreground">Budget</p>
                            <div className="flex items-center gap-2">
                              <DollarSign className="h-4 w-4 text-primary" />
                              <p className="font-medium">{tripIdeas[selectedIdea].budget}</p>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>

                    <div className="space-y-6">
                      <div>
                        <h3 className="text-sm font-medium mb-2">Squad</h3>
                        <div className="flex items-center gap-3 p-3 rounded-md border">
                          <div className="flex -space-x-2 overflow-hidden">
                            {(squads.find((s) => s.id === selectedSquad)?.members || [])
                              .slice(0, 3)
                              .map((member, i) => (
                                <Avatar key={i} className="border-2 border-background h-8 w-8">
                                  <AvatarImage src={member.avatar} alt={member.name} />
                                  <AvatarFallback>
                                    {member.name?.charAt(0) || (i + 1).toString()}
                                  </AvatarFallback>
                                </Avatar>
                              ))}
                          </div>
                          <div>
                            <p className="font-medium">
                              {squads.find((s) => s.id === selectedSquad)?.name}
                            </p>
                            <p className="text-sm text-muted-foreground">
                              {squads.find((s) => s.id === selectedSquad)?.members?.length ||
                                squads.find((s) => s.id === selectedSquad)?.memberCount ||
                                0}{" "}
                              members
                            </p>
                          </div>
                        </div>
                      </div>

                      <div>
                        <h3 className="text-sm font-medium mb-2">Trip Dates</h3>
                        <div className="flex items-center gap-2 p-3 rounded-md border">
                          <Calendar className="h-4 w-4 text-primary" />
                          <Input
                            placeholder="e.g., Aug 15-20, 2023"
                            defaultValue="Aug 15-20, 2023"
                            className="border-0 p-0 h-auto focus-visible:ring-0"
                          />
                        </div>
                      </div>

                      <div>
                        <h3 className="text-sm font-medium mb-2">Booking Preferences</h3>
                        <div className="space-y-3">
                          <div className="flex items-start gap-2">
                            <div className="mt-1">
                              <Checkbox id="flights" defaultChecked />
                            </div>
                            <div>
                              <Label htmlFor="flights" className="font-medium">
                                Flights
                              </Label>
                              <p className="text-sm text-muted-foreground">
                                BroTrips.ai will help coordinate flight bookings through partner
                                sites
                              </p>
                            </div>
                          </div>
                          <div className="flex items-start gap-2">
                            <div className="mt-1">
                              <Checkbox id="accommodation" defaultChecked />
                            </div>
                            <div>
                              <Label htmlFor="accommodation" className="font-medium">
                                Accommodation
                              </Label>
                              <p className="text-sm text-muted-foreground">
                                BroTrips.ai will suggest accommodation options for your group
                              </p>
                            </div>
                          </div>
                          <div className="flex items-start gap-2">
                            <div className="mt-1">
                              <Checkbox id="activities" defaultChecked />
                            </div>
                            <div>
                              <Label htmlFor="activities" className="font-medium">
                                Activities
                              </Label>
                              <p className="text-sm text-muted-foreground">
                                BroTrips.ai will recommend activities based on your preferences
                              </p>
                            </div>
                          </div>
                        </div>
                      </div>

                      <div>
                        <h3 className="text-sm font-medium mb-2">Create Savings Goal</h3>
                        <div className="flex items-start gap-2">
                          <div className="mt-1">
                            <Checkbox id="create-savings" defaultChecked />
                          </div>
                          <div>
                            <Label htmlFor="create-savings" className="font-medium">
                              Set up a savings goal for this trip
                            </Label>
                            <p className="text-sm text-muted-foreground">
                              Track your progress and save towards your trip budget
                            </p>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </CardContent>
                <CardFooter className="flex justify-between">
                  <Button variant="outline" onClick={prevStep}>
                    <ArrowLeft className="mr-2 h-4 w-4" /> Back
                  </Button>
                  <Link href="/trips/1">
                    <Button>Create Trip</Button>
                  </Link>
                </CardFooter>
              </Card>
            </div>
          )}
        </main>
      </div>
    </div>
  )
}

const squads = [
  {
    id: "1",
    name: "College Buddies",
    members: [
      { name: "John Doe", avatar: "/placeholder.svg?height=40&width=40" },
      { name: "Mike Smith", avatar: "/placeholder.svg?height=40&width=40" },
      { name: "Alex Johnson", avatar: "/placeholder.svg?height=40&width=40" },
      { name: "Chris Williams", avatar: "/placeholder.svg?height=40&width=40" },
      { name: "David Brown", avatar: "/placeholder.svg?height=40&width=40" },
    ],
  },
  {
    id: "2",
    name: "Work Friends",
    members: [
      { name: "John Doe", avatar: "/placeholder.svg?height=40&width=40" },
      { name: "Sarah Miller", avatar: "/placeholder.svg?height=40&width=40" },
      { name: "James Wilson", avatar: "/placeholder.svg?height=40&width=40" },
    ],
  },
  {
    id: "3",
    name: "Hiking Crew",
    members: [
      { name: "John Doe", avatar: "/placeholder.svg?height=40&width=40" },
      { name: "Robert Davis", avatar: "/placeholder.svg?height=40&width=40" },
      { name: "Emma Garcia", avatar: "/placeholder.svg?height=40&width=40" },
      { name: "Olivia Martinez", avatar: "/placeholder.svg?height=40&width=40" },
    ],
  },
]

const tripIdeas = [
  {
    destination: "Yellowstone National Park",
    image: "/placeholder.svg?height=150&width=300",
    tags: ["Nature", "Adventure", "Summer"],
    description:
      "Explore the geysers, wildlife, and stunning landscapes of America's first national park.",
    budget: "$1,200 - $1,800 per person",
    duration: "5-7 days",
  },
  {
    destination: "New Orleans",
    image: "/placeholder.svg?height=150&width=300",
    tags: ["Culture", "Food", "Spring"],
    description: "Experience the vibrant culture, music, and cuisine of the Big Easy.",
    budget: "$800 - $1,200 per person",
    duration: "3-5 days",
  },
  {
    destination: "San Diego",
    image: "/placeholder.svg?height=150&width=300",
    tags: ["Beach", "Relaxation", "Year-round"],
    description:
      "Enjoy perfect weather, beautiful beaches, and laid-back vibes in Southern California.",
    budget: "$1,000 - $1,500 per person",
    duration: "4-6 days",
  },
  {
    destination: "Colorado Mountains",
    image: "/placeholder.svg?height=150&width=300",
    tags: ["Adventure", "Outdoors", "Winter"],
    description: "Hit the slopes or hike the trails in this mountain paradise.",
    budget: "$1,100 - $1,700 per person",
    duration: "4-7 days",
  },
]

const tripTypes = [
  { id: "beach", name: "Beach", icon: <MapPin className="h-6 w-6 text-primary" /> },
  { id: "city", name: "City", icon: <Users className="h-6 w-6 text-primary" /> },
  { id: "mountain", name: "Mountain", icon: <MapPin className="h-6 w-6 text-primary" /> },
  { id: "adventure", name: "Adventure", icon: <Sparkles className="h-6 w-6 text-primary" /> },
  { id: "cultural", name: "Cultural", icon: <Utensils className="h-6 w-6 text-primary" /> },
  { id: "relaxation", name: "Relaxation", icon: <Hotel className="h-6 w-6 text-primary" /> },
  { id: "road", name: "Road Trip", icon: <MapPin className="h-6 w-6 text-primary" /> },
  { id: "cruise", name: "Cruise", icon: <Plane className="h-6 w-6 text-primary" /> },
]

const activities = [
  "Sightseeing",
  "Hiking",
  "Beach",
  "Shopping",
  "Nightlife",
  "Museums",
  "Food Tours",
  "Water Sports",
  "Theme Parks",
  "Wildlife",
  "Photography",
  "Local Events",
]
