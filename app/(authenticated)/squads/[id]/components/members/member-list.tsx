"use client"

import { useState } from "react"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { MoreHorizontal, ChevronLeft, ChevronRight, Crown } from "lucide-react"
import { User } from "@/lib/domains/user/user.types"
import { UserDisplay } from "@/components/user-display"

import { toast } from "@/components/ui/use-toast"
import { useSquadMembers } from "@/lib/domains/squad/squad.hooks"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog"

interface MemberWithSubscription extends User {
  isSubscribed?: boolean
}

interface MemberListProps {
  members: MemberWithSubscription[]
  squadId: string
  squadLeaderId: string
  isCurrentUserSquadLead: boolean
  currentPage?: number
  totalPages?: number
  onPageChange?: (page: number) => void
  onMemberRemoved?: () => void
}

export function MemberList({
  members,
  squadId,
  squadLeaderId,
  isCurrentUserSquadLead,
  currentPage = 1,
  totalPages = 1,
  onPageChange,
  onMemberRemoved,
}: MemberListProps) {
  const [loadingStatus, setLoadingStatus] = useState<Record<string, boolean>>({})
  const [memberToRemove, setMemberToRemove] = useState<MemberWithSubscription | null>(null)
  const { remove } = useSquadMembers(squadId)

  const handleRemoveMember = async (member: MemberWithSubscription) => {
    if (!member.uid) return

    setLoadingStatus((prev) => ({ ...prev, [member.uid]: true }))

    try {
      const success = await remove(member.uid)

      if (success) {
        toast({
          title: "Member removed",
          description: `${member.displayName || member.email} has been removed from the squad.`,
        })
        onMemberRemoved?.()
      } else {
        toast({
          title: "Error",
          description: "Failed to remove member. Please try again.",
          variant: "destructive",
        })
      }
    } catch (err) {
      console.error("Error removing member:", err)
      toast({
        title: "Error",
        description: "An unexpected error occurred. Please try again.",
        variant: "destructive",
      })
    } finally {
      setLoadingStatus((prev) => ({ ...prev, [member.uid]: false }))
      setMemberToRemove(null)
    }
  }

  const handleMemberAction = (_memberId: string, action: string) => {
    toast({
      title: "Feature coming soon",
      description: `${action} feature will be available soon.`,
    })
  }

  if (members.length === 0) {
    return (
      <Card>
        <CardContent className="p-6 text-center">
          <p className="text-muted-foreground">No members found</p>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className="space-y-4">
      <Card>
        <CardContent className="p-0">
          <div className="divide-y">
            {members.map((member, index) => (
              <div key={member.uid || index} className="flex items-center justify-between p-4">
                <div className="flex flex-col">
                  <div className="flex items-center gap-2">
                    <UserDisplay
                      displayName={member.displayName}
                      photoURL={member.photoURL}
                      showBadge={false} /* We'll show the badge separately */
                    />
                    {member.isSubscribed && (
                      <div className="flex items-center bg-gradient-to-r from-yellow-400 to-orange-500 px-2 py-0.5 rounded-full">
                        <Crown className="h-3 w-3 text-white" />
                        <span className="text-xs ml-1 text-white font-medium">Pro</span>
                      </div>
                    )}
                  </div>
                  <p className="text-sm text-muted-foreground ml-10">{member.email}</p>
                </div>
                <div className="flex items-center gap-2">
                  {member.uid === squadLeaderId && <Badge variant="outline">Squad Lead</Badge>}
                  {isCurrentUserSquadLead && member.uid !== squadLeaderId && (
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="icon" disabled={loadingStatus[member.uid]}>
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem
                          onClick={() => handleMemberAction(member.uid, "Promote to lead")}
                        >
                          Promote to lead
                        </DropdownMenuItem>
                        <DropdownMenuItem
                          onClick={() => setMemberToRemove(member)}
                          className="text-destructive"
                        >
                          Remove from squad
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  )}
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {totalPages > 1 && (
        <div className="flex justify-center items-center gap-2 mt-4">
          <Button
            variant="outline"
            size="sm"
            onClick={() => onPageChange?.(currentPage - 1)}
            disabled={currentPage === 1}
          >
            <ChevronLeft className="h-4 w-4" />
          </Button>
          <span className="text-sm">
            Page {currentPage} of {totalPages}
          </span>
          <Button
            variant="outline"
            size="sm"
            onClick={() => onPageChange?.(currentPage + 1)}
            disabled={currentPage === totalPages}
          >
            <ChevronRight className="h-4 w-4" />
          </Button>
        </div>
      )}

      {/* Confirmation Dialog */}
      <AlertDialog open={!!memberToRemove} onOpenChange={() => setMemberToRemove(null)}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Remove Squad Member</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to remove{" "}
              <strong>{memberToRemove?.displayName || memberToRemove?.email}</strong> from the
              squad? This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={() => memberToRemove && handleRemoveMember(memberToRemove)}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              Remove Member
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  )
}
