"use client"

import { useState, useRef, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent } from "@/components/ui/card"
import { Ta<PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Send, Paperclip, Image, Smile, Bot, ArrowLeft, MoreHorizontal, Users } from "lucide-react"
import Link from "next/link"

export default function SquadChatPage({ params }: { params: { id: string } }) {
  const [message, setMessage] = useState("")
  const [messages, setMessages] = useState<Message[]>(initialMessages)
  const [isTyping, setIsTyping] = useState(false)
  const messagesEndRef = useRef<HTMLDivElement>(null)
  const [activeTab, setActiveTab] = useState("chat")

  // Scroll to bottom of messages
  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" })
  }

  useEffect(() => {
    scrollToBottom()
  }, [messages])

  // Send message
  const sendMessage = () => {
    if (message.trim() === "") return

    // Add user message
    const newMessage: Message = {
      id: String(Date.now()),
      content: message,
      sender: {
        id: "current-user",
        name: "John Doe",
        avatar: "/placeholder.svg?height=40&width=40",
      },
      timestamp: new Date().toISOString(),
      type: "text",
    }

    setMessages([...messages, newMessage])
    setMessage("")

    // Simulate AI response if message contains trigger words
    if (
      message.toLowerCase().includes("suggest") ||
      message.toLowerCase().includes("recommend") ||
      message.toLowerCase().includes("plan")
    ) {
      simulateAIResponse()
    }
  }

  // Simulate AI response
  const simulateAIResponse = () => {
    setIsTyping(true)

    setTimeout(() => {
      const aiResponse: Message = {
        id: String(Date.now() + 1),
        content:
          "Based on your squad's preferences, I recommend checking out Yellowstone National Park for your next trip. The best time to visit is June through September when the weather is mild and all park facilities are open. Would you like me to suggest some activities or accommodations?",
        sender: {
          id: "ai-assistant",
          name: "BroTrips.ai AI",
          avatar: "/placeholder.svg?height=40&width=40",
        },
        timestamp: new Date().toISOString(),
        type: "ai",
      }

      setMessages((prev) => [...prev, aiResponse])
      setIsTyping(false)
    }, 2000)
  }

  return (
    <div className="min-h-screen flex flex-col">
      <AppHeader />

      <div className="flex-1 flex">
        <AppSidebar />

        <main className="flex-1 flex flex-col">
          <div className="border-b p-4 flex items-center justify-between">
            <div className="flex items-center gap-3">
              <Link href={`/squads/${params.id}`}>
                <Button variant="ghost" size="icon">
                  <ArrowLeft className="h-5 w-5" />
                </Button>
              </Link>
              <div>
                <h1 className="text-lg font-bold">College Buddies</h1>
                <div className="flex items-center gap-1 text-sm text-muted-foreground">
                  <Users className="h-3 w-3" />
                  <span>5 members</span>
                </div>
              </div>
            </div>
            <div className="flex items-center gap-2">
              <Button variant="ghost" size="icon">
                <MoreHorizontal className="h-5 w-5" />
              </Button>
            </div>
          </div>

          <Tabs defaultValue="chat" className="flex-1 flex flex-col" onValueChange={setActiveTab}>
            <div className="border-b px-4">
              <TabsList className="h-10">
                <TabsTrigger value="chat">Chat</TabsTrigger>
                <TabsTrigger value="media">Media</TabsTrigger>
                <TabsTrigger value="files">Files</TabsTrigger>
              </TabsList>
            </div>

            <TabsContent value="chat" className="flex-1 flex flex-col p-0 m-0">
              <div className="flex-1 overflow-y-auto p-4 space-y-4">
                {messages.map((msg) => (
                  <div
                    key={msg.id}
                    className={`flex ${msg.sender.id === "current-user" ? "justify-end" : "justify-start"}`}
                  >
                    <div
                      className={`flex gap-3 max-w-[80%] ${msg.sender.id === "current-user" ? "flex-row-reverse" : ""}`}
                    >
                      <Avatar className="h-8 w-8 mt-1">
                        <AvatarImage src={msg.sender.avatar} alt={msg.sender.name} />
                        <AvatarFallback>{msg.sender.name.charAt(0)}</AvatarFallback>
                      </Avatar>

                      <div>
                        <div className="flex items-center gap-2 mb-1">
                          <span className="text-sm font-medium">{msg.sender.name}</span>
                          <span className="text-xs text-muted-foreground">
                            {new Date(msg.timestamp).toLocaleTimeString([], {
                              hour: "2-digit",
                              minute: "2-digit",
                            })}
                          </span>
                          {msg.type === "ai" && (
                            <Badge
                              variant="outline"
                              className="h-5 px-1 text-xs flex items-center gap-1"
                            >
                              <Bot className="h-3 w-3" /> AI
                            </Badge>
                          )}
                        </div>

                        <div
                          className={`p-3 rounded-lg ${
                            msg.sender.id === "current-user"
                              ? "bg-primary text-primary-foreground"
                              : msg.type === "ai"
                                ? "bg-secondary/20 border"
                                : "bg-muted"
                          }`}
                        >
                          {msg.content}
                        </div>
                      </div>
                    </div>
                  </div>
                ))}

                {isTyping && (
                  <div className="flex justify-start">
                    <div className="flex gap-3 max-w-[80%]">
                      <Avatar className="h-8 w-8 mt-1">
                        <AvatarImage src="/placeholder.svg?height=40&width=40" alt="BroTrip AI" />
                        <AvatarFallback>AI</AvatarFallback>
                      </Avatar>

                      <div>
                        <div className="flex items-center gap-2 mb-1">
                          <span className="text-sm font-medium">BroTrip AI</span>
                          <Badge
                            variant="outline"
                            className="h-5 px-1 text-xs flex items-center gap-1"
                          >
                            <Bot className="h-3 w-3" /> AI
                          </Badge>
                        </div>

                        <div className="p-3 rounded-lg bg-secondary/20 border">
                          <div className="flex gap-1">
                            <div className="h-2 w-2 rounded-full bg-current animate-bounce"></div>
                            <div
                              className="h-2 w-2 rounded-full bg-current animate-bounce"
                              style={{ animationDelay: "0.2s" }}
                            ></div>
                            <div
                              className="h-2 w-2 rounded-full bg-current animate-bounce"
                              style={{ animationDelay: "0.4s" }}
                            ></div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                )}

                <div ref={messagesEndRef} />
              </div>

              <div className="border-t p-4">
                <div className="flex gap-2">
                  <Button variant="outline" size="icon">
                    <Paperclip className="h-5 w-5" />
                  </Button>
                  <Button variant="outline" size="icon">
                    <Image className="h-5 w-5" />
                  </Button>
                  <Button variant="outline" size="icon">
                    <Smile className="h-5 w-5" />
                  </Button>
                  <div className="flex-1 relative">
                    <Input
                      placeholder="Type a message..."
                      value={message}
                      onChange={(e) => setMessage(e.target.value)}
                      onKeyDown={(e) => {
                        if (e.key === "Enter" && !e.shiftKey) {
                          e.preventDefault()
                          sendMessage()
                        }
                      }}
                      className="pr-10"
                    />
                    <Button
                      variant="ghost"
                      size="icon"
                      className="absolute right-0 top-0 h-full"
                      onClick={sendMessage}
                      disabled={message.trim() === ""}
                    >
                      <Send className="h-5 w-5" />
                    </Button>
                  </div>
                </div>
                <div className="mt-2 text-xs text-muted-foreground">
                  <span>Tip: Ask the AI assistant to </span>
                  <span
                    className="text-primary cursor-pointer"
                    onClick={() => setMessage("Suggest activities for our Lake Tahoe trip")}
                  >
                    suggest activities
                  </span>
                  <span> or </span>
                  <span
                    className="text-primary cursor-pointer"
                    onClick={() => setMessage("Plan a weekend trip for our squad")}
                  >
                    plan a trip
                  </span>
                </div>
              </div>
            </TabsContent>

            <TabsContent value="media" className="flex-1 p-4">
              <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-4">
                {mediaItems.map((item, index) => (
                  <Card key={index} className="overflow-hidden">
                    <div className="aspect-square relative">
                      <img
                        src={
                          item.url && item.url.trim() !== ""
                            ? item.url
                            : "/placeholder.svg?height=200&width=200"
                        }
                        alt={`Media ${index}`}
                        className="object-cover w-full h-full"
                      />
                    </div>
                    <CardContent className="p-2">
                      <p className="text-xs text-muted-foreground truncate">
                        Shared by {item.sender} • {item.date}
                      </p>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </TabsContent>

            <TabsContent value="files" className="flex-1 p-4">
              <div className="space-y-2">
                {fileItems.map((file, index) => (
                  <div
                    key={index}
                    className="flex items-center justify-between p-3 rounded-md border"
                  >
                    <div className="flex items-center gap-3">
                      <div className="h-10 w-10 rounded-md bg-primary/10 flex items-center justify-center">
                        {file.icon}
                      </div>
                      <div>
                        <p className="font-medium">{file.name}</p>
                        <p className="text-xs text-muted-foreground">
                          {file.size} • Shared by {file.sender} • {file.date}
                        </p>
                      </div>
                    </div>
                    <Button variant="outline" size="sm">
                      Download
                    </Button>
                  </div>
                ))}
              </div>
            </TabsContent>
          </Tabs>
        </main>
      </div>
    </div>
  )
}

interface Message {
  id: string
  content: string
  sender: {
    id: string
    name: string
    avatar: string
  }
  timestamp: string
  type: "text" | "image" | "file" | "ai"
}

const initialMessages: Message[] = [
  {
    id: "1",
    content: "Hey everyone! I'm thinking about planning our next trip. Any ideas?",
    sender: {
      id: "user-1",
      name: "Mike Smith",
      avatar: "/placeholder.svg?height=40&width=40",
    },
    timestamp: "2023-07-01T10:30:00Z",
    type: "text",
  },
  {
    id: "2",
    content: "I've been wanting to check out Lake Tahoe! I heard it's beautiful in August.",
    sender: {
      id: "current-user",
      name: "John Doe",
      avatar: "/placeholder.svg?height=40&width=40",
    },
    timestamp: "2023-07-01T10:32:00Z",
    type: "text",
  },
  {
    id: "3",
    content: "That sounds awesome! I'm definitely in for Lake Tahoe.",
    sender: {
      id: "user-2",
      name: "Alex Johnson",
      avatar: "/placeholder.svg?height=40&width=40",
    },
    timestamp: "2023-07-01T10:35:00Z",
    type: "text",
  },
  {
    id: "4",
    content: "Lake Tahoe would be perfect for hiking and water activities. When were you thinking?",
    sender: {
      id: "user-3",
      name: "Chris Williams",
      avatar: "/placeholder.svg?height=40&width=40",
    },
    timestamp: "2023-07-01T10:40:00Z",
    type: "text",
  },
  {
    id: "5",
    content: "I'm thinking mid-August, maybe the 15th to the 20th? Would that work for everyone?",
    sender: {
      id: "user-1",
      name: "Mike Smith",
      avatar: "/placeholder.svg?height=40&width=40",
    },
    timestamp: "2023-07-01T10:45:00Z",
    type: "text",
  },
  {
    id: "6",
    content: "Works for me! Should we ask the AI assistant for some recommendations?",
    sender: {
      id: "current-user",
      name: "John Doe",
      avatar: "/placeholder.svg?height=40&width=40",
    },
    timestamp: "2023-07-01T10:50:00Z",
    type: "text",
  },
  {
    id: "7",
    content:
      "Hey BroTrip AI, can you suggest some activities and accommodations for Lake Tahoe in mid-August?",
    sender: {
      id: "user-2",
      name: "Alex Johnson",
      avatar: "/placeholder.svg?height=40&width=40",
    },
    timestamp: "2023-07-01T10:55:00Z",
    type: "text",
  },
  {
    id: "8",
    content:
      "For Lake Tahoe in mid-August, I'd recommend staying near South Lake Tahoe for the best access to activities. For accommodations, consider renting a cabin that can fit your whole group - they typically range from $250-400/night. Popular activities include hiking the Emerald Bay Trail, water sports on the lake (jet skiing, paddleboarding), and taking the Heavenly Mountain Gondola for amazing views. The weather should be perfect with highs around 80°F. Would you like me to create a draft itinerary for your trip?",
    sender: {
      id: "ai-assistant",
      name: "BroTrip AI",
      avatar: "/placeholder.svg?height=40&width=40",
    },
    timestamp: "2023-07-01T10:56:00Z",
    type: "ai",
  },
]

const mediaItems = [
  { url: "/placeholder.svg?height=200&width=200", sender: "Mike Smith", date: "Jul 1, 2023" },
  { url: "/placeholder.svg?height=200&width=200", sender: "Alex Johnson", date: "Jul 2, 2023" },
  { url: "/placeholder.svg?height=200&width=200", sender: "Chris Williams", date: "Jul 3, 2023" },
  { url: "/placeholder.svg?height=200&width=200", sender: "John Doe", date: "Jul 4, 2023" },
  { url: "/placeholder.svg?height=200&width=200", sender: "Mike Smith", date: "Jul 5, 2023" },
  { url: "/placeholder.svg?height=200&width=200", sender: "Alex Johnson", date: "Jul 6, 2023" },
]

const fileItems = [
  {
    name: "Lake_Tahoe_Itinerary.pdf",
    size: "2.4 MB",
    sender: "Mike Smith",
    date: "Jul 1, 2023",
    icon: (
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width="24"
        height="24"
        viewBox="0 0 24 24"
        fill="none"
        stroke="currentColor"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
        className="text-red-500"
      >
        <path d="M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z" />
        <polyline points="14 2 14 8 20 8" />
        <path d="M9 15v-2h6v2" />
        <path d="M11 13v4" />
        <path d="M9 19h6" />
      </svg>
    ),
  },
  {
    name: "Packing_List.docx",
    size: "1.2 MB",
    sender: "Alex Johnson",
    date: "Jul 2, 2023",
    icon: (
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width="24"
        height="24"
        viewBox="0 0 24 24"
        fill="none"
        stroke="currentColor"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
        className="text-blue-500"
      >
        <path d="M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z" />
        <polyline points="14 2 14 8 20 8" />
        <path d="M16 13H8" />
        <path d="M16 17H8" />
        <path d="M10 9H8" />
      </svg>
    ),
  },
  {
    name: "Budget_Spreadsheet.xlsx",
    size: "3.1 MB",
    sender: "Chris Williams",
    date: "Jul 3, 2023",
    icon: (
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width="24"
        height="24"
        viewBox="0 0 24 24"
        fill="none"
        stroke="currentColor"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
        className="text-green-500"
      >
        <path d="M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z" />
        <polyline points="14 2 14 8 20 8" />
        <rect x="8" y="12" width="8" height="6" />
        <path d="M10 12v6" />
        <path d="M14 12v6" />
        <path d="M8 15h8" />
      </svg>
    ),
  },
  {
    name: "Cabin_Options.zip",
    size: "15.7 MB",
    sender: "John Doe",
    date: "Jul 4, 2023",
    icon: (
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width="24"
        height="24"
        viewBox="0 0 24 24"
        fill="none"
        stroke="currentColor"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
        className="text-yellow-500"
      >
        <path d="M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z" />
        <polyline points="14 2 14 8 20 8" />
        <path d="M12 18v-6" />
        <path d="m9 15 3 3 3-3" />
      </svg>
    ),
  },
]
