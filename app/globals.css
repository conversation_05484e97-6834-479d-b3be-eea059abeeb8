@tailwind base;
@tailwind components;
@tailwind utilities;

body {
  font-family: Arial, Helvetica, sans-serif;
  /* Prevent layout shifts from scrollbar appearance/disappearance */
  overflow-y: scroll;
}

/* Logo color scheme support - uses CSS custom properties for dynamic theming */
.logo-fill {
  fill: hsl(var(--primary)); /* Uses primary color (Deep Teal in light mode, white in dark mode) */
  transition: fill 0.2s ease-in-out;
}

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }

  /* Hide scrollbar for Chrome, Safari and Opera */
  .no-scrollbar::-webkit-scrollbar {
    display: none;
  }

  /* Hide scrollbar for IE, Edge and Firefox */
  .no-scrollbar {
    -ms-overflow-style: none;  /* IE and Edge */
    scrollbar-width: none;  /* Firefox */
  }

  /* Thin scrollbar for better mobile UX */
  .scrollbar-thin::-webkit-scrollbar {
    height: 4px;
    width: 4px;
  }

  .scrollbar-thin::-webkit-scrollbar-track {
    background: transparent;
  }

  .scrollbar-thin::-webkit-scrollbar-thumb {
    background: hsl(var(--muted-foreground));
    border-radius: 4px;
  }

  .scrollbar-thin {
    scrollbar-width: thin;
    scrollbar-color: hsl(var(--muted-foreground)) transparent;
  }

  /* Floating AI Button - ensure proper viewport positioning */
  .floating-ai-button {
    position: fixed !important;
    bottom: 1.5rem !important;
    right: 1.5rem !important;
    z-index: 60 !important;
    pointer-events: none !important;
    width: 56px !important;
    height: 56px !important;
  }

  .floating-ai-button button {
    pointer-events: auto !important;
  }

  /* Prevent tooltip layout shifts */
  [data-radix-tooltip-content] {
    position: fixed !important;
    z-index: 9999 !important;
    pointer-events: none !important;
  }

  /* Ensure tooltip triggers don't cause layout shifts - only for buttons and interactive elements */
  button[data-radix-tooltip-trigger],
  [role="button"][data-radix-tooltip-trigger] {
    display: inline-flex !important;
    align-items: center !important;
    justify-content: center !important;
  }

  /* Prevent dropdown menu layout shifts */
  [data-radix-dropdown-menu-content] {
    position: fixed !important;
    z-index: 50 !important;
    max-width: calc(100vw - 2rem) !important;
    /* Prevent content from causing horizontal scrollbars */
    box-sizing: border-box !important;
    /* Ensure dropdown doesn't affect document flow */
    transform: translateZ(0) !important;
  }

  /* Prevent all radix overlays from causing layout shifts */
  [data-radix-popper-content-wrapper] {
    position: fixed !important;
    z-index: 50 !important;
  }
}

@layer base {
  :root {
    --background: #F5F5F5;        /* Warm White */
    --foreground: #212121;        /* Rich Black for headings */
    --card: 0 0% 100%;            /* Pure white for cards to contrast with warm white background */
    --card-foreground: 0 0% 25%;  /* Rich Black for card text */
    --popover: 0 0% 100%;         /* Pure white for popovers/toasts */
    --popover-foreground: 0 0% 25%; /* Rich Black for popover text */
    --primary: 184 100% 24%;      /* Deep Teal #00796B for buttons */
    --primary-foreground: 0 0% 96.1%; /* Warm White text on teal buttons */
    --secondary: 0 0% 92%;        /* More contrast from background for visibility */
    --secondary-foreground: 0 0% 25%; /* Rich Black for secondary elements */
    --muted: 0 0% 92%;            /* More contrast from background for visibility */
    --muted-foreground: 0 0% 40%; /* Gray body text (neutral-600 equivalent) */
    --accent: 184 100% 24%;       /* Deep Teal for accents/icons */
    --accent-foreground: 0 0% 96.1%; /* Warm White text on teal accents */
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 96.1%;  /* Changed from 98% to match #F5F5F5 */
    --border: 0 0% 89.8%;
    --input: 0 0% 85%;            /* Light gray for unchecked switch background */
    --ring: 0 0% 3.9%;
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
    --radius: 0.5rem;
    --sidebar-background: 0 0% 96.1%;     /* Warm white for sidebar background */
    --sidebar-foreground: 0 0% 25%;       /* Rich black for sidebar text */
    --sidebar-primary: 184 100% 24%;      /* Deep teal for sidebar primary elements */
    --sidebar-primary-foreground: 0 0% 96.1%; /* Warm white text on teal */
    --sidebar-accent: 0 0% 92%;           /* Slightly darker for hover states */
    --sidebar-accent-foreground: 0 0% 25%; /* Rich black for accent text */
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 217.2 91.2% 59.8%;

    /* Custom teal colors for icons and backgrounds */
    --teal-600: 184 81% 29%;      /* Teal-600 equivalent for icons */
    --teal-100: 184 27% 94%;      /* Light teal background for icons */
  }
  .dark {
    --background: 0 0% 3.9%;
    --foreground: 0 0% 98%;
    --card: 0 0% 3.9%;
    --card-foreground: 0 0% 98%;
    --popover: 0 0% 3.9%;
    --popover-foreground: 0 0% 98%;
    --primary: 0 0% 98%;
    --primary-foreground: 0 0% 9%;
    --secondary: 0 0% 14.9%;
    --secondary-foreground: 0 0% 98%;
    --muted: 0 0% 14.9%;
    --muted-foreground: 0 0% 63.9%;
    --accent: 0 0% 14.9%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 14.9%;
    --input: 0 0% 14.9%;
    --ring: 0 0% 83.1%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
    overflow-x: hidden;
  }
  html {
    overflow-x: hidden;
  }
}

/* Theme transition styles */
:root {
  --theme-transition-duration: 300ms;
}

/* Apply transitions to the html and body elements */
html,
body {
  transition: background-color var(--theme-transition-duration) ease,
              color var(--theme-transition-duration) ease;
}

/* Prevent transitions during theme change to avoid flickering */
.theme-changing * {
  transition: none !important;
}

/* Apply transitions to common UI elements */
.theme-transition,
.theme-transition * {
  transition: background-color var(--theme-transition-duration) ease,
              border-color var(--theme-transition-duration) ease,
              color var(--theme-transition-duration) ease,
              fill var(--theme-transition-duration) ease,
              stroke var(--theme-transition-duration) ease,
              opacity var(--theme-transition-duration) ease;
}

/* Ensure content is visible after theme is applied */
.theme-aware-content {
  opacity: 1;
  transition: opacity var(--theme-transition-duration) ease;
}

/* Hide content during theme changes */
.theme-changing .theme-aware-content {
  opacity: 0.5;
}

/* Light mode UI fixes - using :root selector for better specificity */
@layer base {
  /* Override CSS variables for light mode specifically */
  :root:not(.dark) {
    --background: #F5F5F5;        /* Warm White page background */
    --card: 0 0% 100%;            /* Pure white for cards */
    --popover: 0 0% 100%;         /* Pure white for toasts/popovers */
    --sidebar-background: 0 0% 100%; /* Pure white for sidebar */
    --input: 0 0% 85%;            /* Light gray for switch unchecked state */
  }
}

@layer components {
  /* Fix tabs visibility in light mode */
  :root:not(.dark) [data-state="active"] {
    background-color: white !important;
    border: 1px solid hsl(var(--border)) !important;
    box-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1) !important;
  }

  /* Fix sidebar background in light mode - both desktop and mobile */
  :root:not(.dark) aside {
    background-color: white !important;
  }

  /* Fix mobile sidebar (Sheet) background */
  :root:not(.dark) [data-radix-dialog-content].bg-background {
    background-color: white !important;
    border: 1px solid hsl(var(--border)) !important;
  }

  /* Fix toast backgrounds - target the exact classes from inspection */
  :root:not(.dark) .bg-background.border.text-foreground {
    background-color: white !important;
    border: 1px solid hsl(var(--border)) !important;
    box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1) !important;
  }

  /* More specific toast targeting using the exact structure */
  :root:not(.dark) li[role="status"][data-state="open"].bg-background {
    background-color: white !important;
    border: 1px solid hsl(var(--border)) !important;
    box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1) !important;
  }

  /* Even more specific - target the exact toast element */
  :root:not(.dark) .group.pointer-events-auto.bg-background.text-foreground {
    background-color: white !important;
    border: 1px solid hsl(var(--border)) !important;
    box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1) !important;
  }

  /* Fix switch thumb visibility */
  :root:not(.dark) button[role="switch"] > span {
    background-color: white !important;
    border: 1px solid hsl(var(--border)) !important;
    box-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.1) !important;
  }

  /* Override bg-background class for specific components in light mode */
  :root:not(.dark) .bg-background:not(body):not(html) {
    background-color: white !important;
    border: 1px solid hsl(var(--border)) !important;
  }

  /* Ensure toasts and sheets get proper styling */
  :root:not(.dark) [data-radix-dialog-content],
  :root:not(.dark) [data-radix-toast-root],
  :root:not(.dark) li[role="status"] {
    background-color: white !important;
    border: 1px solid hsl(var(--border)) !important;
    box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1) !important;
  }

  /* Fix tab panel focus ring visibility issue - target Tailwind's CSS variables */
  :root:not(.dark) [role="tabpanel"] {
    --tw-ring-offset-color: #F5F5F5 !important; /* Match page background */
    --ring-offset-color: #F5F5F5 !important; /* Fallback */
  }

  /* Fix any element with ring-offset-background class */
  :root:not(.dark) .ring-offset-background {
    --tw-ring-offset-color: #F5F5F5 !important; /* Match page background */
    --ring-offset-color: #F5F5F5 !important; /* Fallback */
  }

  /* More specific targeting for the tab panel */
  :root:not(.dark) div[role="tabpanel"].ring-offset-background {
    --tw-ring-offset-color: #F5F5F5 !important;
    --ring-offset-color: #F5F5F5 !important;
  }

  /* Hide focus rings when not actually focused */
  :root:not(.dark) [role="tabpanel"]:not(:focus-visible) {
    outline: none !important;
    box-shadow: none !important;
    --tw-ring-offset-width: 0px !important;
    --tw-ring-width: 0px !important;
  }

  /* Completely disable ring offset for tab panels in light mode */
  :root:not(.dark) [role="tabpanel"].ring-offset-background {
    --tw-ring-offset-width: 0px !important;
    --tw-ring-offset-color: transparent !important;
    --tw-ring-color: transparent !important;
    --tw-ring-width: 0px !important;
  }

  /* Alternative approach - override the specific element */
  :root:not(.dark) div[data-state="active"][role="tabpanel"] {
    --tw-ring-offset-width: 0px !important;
    --tw-ring-offset-color: transparent !important;
    --tw-ring-color: transparent !important;
    --tw-ring-width: 0px !important;
    outline: none !important;
    box-shadow: none !important;
  }

  /* DEBUG: Let me check what's actually happening */
  :root:not(.dark) [role="tabpanel"] {
    /* Force remove any ring styles completely */
    box-shadow: none !important;
    outline: none !important;
    border: none !important;
  }

  /* Nuclear option - remove all ring-related classes in light mode */
  :root:not(.dark) .ring-offset-background {
    --tw-ring-offset-width: 0 !important;
    --tw-ring-width: 0 !important;
    --tw-ring-offset-color: transparent !important;
    --tw-ring-color: transparent !important;
  }

  /* DEEP FIX: The real issue might be that tabindex="0" makes it focusable */
  /* Remove focus styles completely for tab panels in light mode */
  :root:not(.dark) [role="tabpanel"][tabindex="0"] {
    --tw-ring-offset-width: 0 !important;
    --tw-ring-width: 0 !important;
    --tw-ring-offset-color: transparent !important;
    --tw-ring-color: transparent !important;
    outline: none !important;
    box-shadow: none !important;
  }

  /* Override focus-visible specifically */
  :root:not(.dark) [role="tabpanel"]:focus-visible {
    --tw-ring-offset-width: 0 !important;
    --tw-ring-width: 0 !important;
    outline: none !important;
    box-shadow: none !important;
  }

  /* Override focus state */
  :root:not(.dark) [role="tabpanel"]:focus {
    --tw-ring-offset-width: 0 !important;
    --tw-ring-width: 0 !important;
    outline: none !important;
    box-shadow: none !important;
  }
}
