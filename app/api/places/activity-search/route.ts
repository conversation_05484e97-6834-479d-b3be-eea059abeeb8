import { NextRequest, NextResponse } from "next/server"
import { verifyAuth } from "@/lib/api-auth"
import { FlatSubscriptionService } from "@/lib/domains/user-subscription/flat-subscription.service"

export async function POST(request: NextRequest) {
  try {
    // Verify authentication
    const authResult = await verifyAuth(request)

    if (!authResult.isAuthenticated) {
      return authResult.response
    }

    const userId = authResult.userId

    // Parse request body
    const body = await request.json()
    const { query, location, activityType, destination } = body

    if (!query || !destination) {
      return NextResponse.json({ error: "Query and destination are required" }, { status: 400 })
    }

    // Check subscription status for enhanced features
    const isSubscribed = userId
      ? await FlatSubscriptionService.hasFeatureAccess(userId, "unlimited_ai")
      : false

    const apiKey = process.env.GOOGLE_PLACES_API_KEY

    if (!apiKey) {
      console.error("Google Places API key is not configured")
      return NextResponse.json({ error: "Places service is not configured" }, { status: 500 })
    }

    // Build search query based on activity type and location
    let searchQuery = `${query} in ${destination}`

    // Add type-specific modifiers for better results
    if (activityType === "restaurant" || activityType === "dining") {
      searchQuery = `restaurants ${query} in ${destination}`
    } else if (activityType === "entertainment") {
      searchQuery = `entertainment ${query} in ${destination}`
    } else if (activityType === "attraction") {
      searchQuery = `attractions ${query} in ${destination}`
    }

    // Build search URL with enhanced parameters for Pro users
    let searchUrl = `https://maps.googleapis.com/maps/api/place/textsearch/json?query=${encodeURIComponent(searchQuery)}&key=${apiKey}`

    // Add location bias for Pro users if provided
    if (isSubscribed && location) {
      searchUrl += `&location=${encodeURIComponent(location)}&radius=10000` // 10km radius
    }

    // Add type filter if specified
    if (activityType === "restaurant") {
      searchUrl += `&type=restaurant`
    } else if (activityType === "entertainment") {
      searchUrl += `&type=night_club|amusement_park|tourist_attraction`
    }

    console.log(`Activity Places Search: ${searchQuery}`)

    const response = await fetch(searchUrl)

    if (!response.ok) {
      const errorData = await response.json()
      console.error("Google Places API error:", errorData)
      return NextResponse.json({ error: "Failed to fetch place data" }, { status: response.status })
    }

    const data = await response.json()

    if (data.status !== "OK" || !data.results || data.results.length === 0) {
      console.log("No places found for activity search:", searchQuery)
      return NextResponse.json({
        places: [],
        status: data.status,
      })
    }

    // Process and enhance results
    const enhancedPlaces = data.results.slice(0, 5).map((place: any) => ({
      place_id: place.place_id,
      name: place.name,
      rating: place.rating,
      price_level: place.price_level,
      user_ratings_total: place.user_ratings_total,
      formatted_address: place.formatted_address,
      types: place.types,
      photos:
        place.photos?.slice(0, 3).map((photo: any) => ({
          photo_reference: photo.photo_reference,
          width: photo.width,
          height: photo.height,
        })) || [],
      geometry: place.geometry,
      opening_hours: place.opening_hours
        ? {
            open_now: place.opening_hours.open_now,
          }
        : undefined,
      // Enhanced data for Pro users
      ...(isSubscribed && {
        vicinity: place.vicinity,
        plus_code: place.plus_code,
        business_status: place.business_status,
      }),
    }))

    return NextResponse.json({
      places: enhancedPlaces,
      status: "OK",
      isProUser: isSubscribed,
    })
  } catch (error) {
    console.error("Error in activity places search:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}
