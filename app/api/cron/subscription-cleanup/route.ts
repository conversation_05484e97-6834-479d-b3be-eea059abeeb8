import { NextRequest, NextResponse } from "next/server"
import { FlatSubscriptionCronService } from "@/lib/domains/user-subscription/flat-subscription-cron.service"

/**
 * Cron job endpoint for cleaning up expired subscription entries
 *
 * This endpoint should be called weekly by a cron service
 *
 * Expected to be called with:
 * - Authorization header with cron secret
 * - POST method
 */
export async function POST(request: NextRequest) {
  try {
    // Verify cron authorization
    const authHeader = request.headers.get("authorization")
    const expectedAuth = `Bearer ${process.env.CRON_SECRET}`

    if (!authHeader || authHeader !== expectedAuth) {
      console.error("Unauthorized cron request")
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    console.log("🧹 Starting subscription cleanup cron job...")

    const result = await FlatSubscriptionCronService.cleanupExpiredEntries()

    if (result.success) {
      const { processedUsers, cleanedEntries, errors, duration } = result.data!

      console.log(`✅ Cleanup cron job completed successfully in ${duration}ms`)

      return NextResponse.json({
        success: true,
        message: "Subscription cleanup completed",
        data: {
          processedUsers,
          cleanedEntries,
          errorCount: errors.length,
          duration,
        },
      })
    } else {
      console.error("❌ Cleanup cron job failed:", result.error?.message)

      return NextResponse.json(
        {
          success: false,
          error: "Subscription cleanup failed",
          message: result.error?.message,
        },
        { status: 500 }
      )
    }
  } catch (error) {
    console.error("💥 Cleanup cron job exception:", error)

    return NextResponse.json(
      {
        success: false,
        error: "Internal server error",
        message: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 }
    )
  }
}

/**
 * Health check endpoint
 */
export async function GET(request: NextRequest) {
  try {
    // Verify authorization for health checks
    const authHeader = request.headers.get("authorization")
    const expectedAuth = `Bearer ${process.env.CRON_SECRET}`

    if (!authHeader || authHeader !== expectedAuth) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    return NextResponse.json({
      success: true,
      message: "Subscription cleanup cron endpoint is healthy",
      timestamp: new Date().toISOString(),
    })
  } catch (error) {
    return NextResponse.json(
      {
        success: false,
        error: "Health check failed",
        message: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 }
    )
  }
}
