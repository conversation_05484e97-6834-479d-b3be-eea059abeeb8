/**
 * Migration script to generate referral codes for existing users
 *
 * This script:
 * 1. Fetches all users who don't have referral codes
 * 2. Generates unique referral codes for them
 * 3. Creates referral/{referralCode} documents
 * 4. Can be safely re-run without affecting users who already have codes
 *
 * Usage:
 * npx tsx scripts/migrate-referral-codes.ts
 * npx tsx scripts/migrate-referral-codes.ts .env.production
 * ENV_FILE=.env.staging npx tsx scripts/migrate-referral-codes.ts
 */

import { initializeApp, getApps, cert } from "firebase-admin/app"
import { getFirestore } from "firebase-admin/firestore"
import { config } from "dotenv"
import { resolve } from "path"

// Load environment variables from specified .env file or default
const envFile = process.env.ENV_FILE || process.argv[2] || ".env.local"
const envPath = resolve(process.cwd(), envFile)

console.log(`Loading environment variables from: ${envPath}`)
config({ path: envPath })

// Initialize Firebase Admin SDK
if (!getApps().length) {
  try {
    if (!process.env.FIREBASE_SERVICE_ACCOUNT_KEY) {
      throw new Error("FIREBASE_SERVICE_ACCOUNT_KEY environment variable is not set")
    }

    const serviceAccount = JSON.parse(process.env.FIREBASE_SERVICE_ACCOUNT_KEY)

    initializeApp({
      credential: cert(serviceAccount),
      databaseURL: `https://${process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID}.firebaseio.com`,
    })
  } catch (error) {
    console.error("Firebase admin initialization error:", error)
    process.exit(1)
  }
}

const db = getFirestore()

/**
 * Generate a unique 8-character alphanumeric referral code
 */
async function generateReferralCode(maxRetries = 10): Promise<string> {
  const characters = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"
  const length = 8

  for (let attempt = 0; attempt < maxRetries; attempt++) {
    let code = ""
    for (let i = 0; i < length; i++) {
      code += characters.charAt(Math.floor(Math.random() * characters.length))
    }

    // Check if code already exists
    const referralDoc = await db.collection("referral").doc(code).get()
    if (!referralDoc.exists) {
      return code
    }

    console.log(`Code ${code} already exists, retrying... (attempt ${attempt + 1}/${maxRetries})`)
  }

  throw new Error(`Failed to generate unique referral code after ${maxRetries} attempts`)
}

/**
 * Check if a user already has a referral code
 */
async function userHasReferralCode(userId: string): Promise<boolean> {
  const referralQuery = await db.collection("referral").where("userId", "==", userId).limit(1).get()

  return !referralQuery.empty
}

/**
 * Create a referral code for a user
 */
async function createReferralCodeForUser(userId: string, code: string): Promise<void> {
  await db.collection("referral").doc(code).set({
    userId,
    totalReferrals: 0,
    isActive: true,
    createdAt: new Date(),
    updatedAt: new Date(),
  })
}

/**
 * Main migration function
 */
async function migrateReferralCodes() {
  console.log("Starting referral code migration...")

  try {
    // Get all users
    const usersSnapshot = await db.collection("users").get()
    console.log(`Found ${usersSnapshot.size} users`)

    let processedCount = 0
    let skippedCount = 0
    let errorCount = 0

    for (const userDoc of usersSnapshot.docs) {
      const userId = userDoc.id
      const userData = userDoc.data()

      try {
        // Check if user already has a referral code
        const hasCode = await userHasReferralCode(userId)

        if (hasCode) {
          console.log(`User ${userId} (${userData.email}) already has a referral code, skipping`)
          skippedCount++
          continue
        }

        // Generate and create referral code
        const referralCode = await generateReferralCode()
        await createReferralCodeForUser(userId, referralCode)

        console.log(`Created referral code ${referralCode} for user ${userId} (${userData.email})`)
        processedCount++

        // Add a small delay to avoid overwhelming Firestore
        await new Promise((resolve) => setTimeout(resolve, 100))
      } catch (error) {
        console.error(`Error processing user ${userId}:`, error)
        errorCount++
      }
    }

    console.log("\nMigration completed!")
    console.log(`Processed: ${processedCount} users`)
    console.log(`Skipped: ${skippedCount} users (already had codes)`)
    console.log(`Errors: ${errorCount} users`)
  } catch (error) {
    console.error("Migration failed:", error)
    process.exit(1)
  }
}

/**
 * Validate migration results
 */
async function validateMigration() {
  console.log("\nValidating migration results...")

  try {
    // Count users
    const usersSnapshot = await db.collection("users").get()
    const userCount = usersSnapshot.size

    // Count referral codes
    const referralSnapshot = await db.collection("referral").get()
    const referralCount = referralSnapshot.size

    console.log(`Total users: ${userCount}`)
    console.log(`Total referral codes: ${referralCount}`)

    if (userCount === referralCount) {
      console.log("✅ Migration validation successful: All users have referral codes")
    } else {
      console.log("⚠️  Migration validation warning: User count doesn't match referral code count")

      // Find users without referral codes
      let usersWithoutCodes = 0
      for (const userDoc of usersSnapshot.docs) {
        const userId = userDoc.id
        const hasCode = await userHasReferralCode(userId)
        if (!hasCode) {
          usersWithoutCodes++
          console.log(`User without referral code: ${userId}`)
        }
      }

      console.log(`Users without referral codes: ${usersWithoutCodes}`)
    }
  } catch (error) {
    console.error("Validation failed:", error)
  }
}

// Run the migration
if (import.meta.url === `file://${process.argv[1]}`) {
  migrateReferralCodes()
    .then(() => validateMigration())
    .then(() => {
      console.log("Migration script completed")
      process.exit(0)
    })
    .catch((error) => {
      console.error("Migration script failed:", error)
      process.exit(1)
    })
}

export { migrateReferralCodes, validateMigration }
