#!/usr/bin/env tsx

/**
 * Clean Migration Script for Flat Subscription Architecture
 *
 * This script migrates existing subscription data to the new flat collection structure.
 *
 * Usage:
 *   npm run migrate:flat-subscriptions [options]
 *
 * Options:
 *   --dry-run          Show what would be migrated without making changes
 *   --user-id <id>     Migrate only a specific user
 *   --validate-only    Only run validation, don't migrate
 *   --rollback         Emergency rollback to original structure
 *   --backup-only      Only backup existing data
 *   --env <path>       Path to environment file (default: .env.local)
 */

import { config } from "dotenv"
import { Command } from "commander"
import { resolve } from "path"
import { initializeApp, cert, getApps } from "firebase-admin/app"
import { getFirestore } from "firebase-admin/firestore"
import { AdminMigrationService } from "../lib/domains/user-subscription/admin-migration.service"

// Load environment variables
const program = new Command()

program
  .name("migrate-flat-subscriptions")
  .description("Migrate subscriptions to flat collection structure")
  .option("--dry-run", "Show what would be migrated without making changes")
  .option("--user-id <id>", "Migrate only a specific user")
  .option("--validate-only", "Only run validation, don't migrate")
  .option("--rollback", "Emergency rollback to original structure")
  .option("--backup-only", "Only backup existing data")
  .option("--env <path>", "Path to environment file", ".env.local")

program.parse()

const options = program.opts()

// Initialize Firebase Admin SDK
async function initializeFirebase() {
  // Load environment variables from specified .env file
  const envPath = resolve(process.cwd(), options.env)
  console.log(`Loading environment variables from: ${envPath}`)
  config({ path: envPath })

  // Initialize Firebase Admin SDK if not already initialized
  if (!getApps().length) {
    try {
      if (!process.env.FIREBASE_SERVICE_ACCOUNT_KEY) {
        throw new Error("FIREBASE_SERVICE_ACCOUNT_KEY environment variable is not set")
      }

      const serviceAccount = JSON.parse(process.env.FIREBASE_SERVICE_ACCOUNT_KEY)

      initializeApp({
        credential: cert(serviceAccount),
        databaseURL: `https://${process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID}.firebaseio.com`,
      })

      console.log("✅ Firebase Admin SDK initialized successfully")
    } catch (error) {
      console.error("❌ Firebase admin initialization error:", error)
      process.exit(1)
    }
  }

  return getFirestore()
}

async function main() {
  try {
    // Initialize Firebase Admin SDK
    const db = await initializeFirebase()

    console.log("🚀 Flat Subscription Migration Tool")
    console.log("===================================")

    if (options.rollback) {
      await performRollback()
      return
    }

    if (options.backupOnly) {
      await performBackupOnly()
      return
    }

    if (options.validateOnly) {
      await validateMigration()
      return
    }

    if (options.userId) {
      await migrateSingleUser(options.userId, options.dryRun)
      return
    }

    if (options.dryRun) {
      await dryRunMigration()
      return
    }

    // Full migration
    await runFullMigration()
  } catch (error) {
    console.error("❌ Migration failed:", error)
    process.exit(1)
  }
}

async function performRollback() {
  console.log("🚨 EMERGENCY ROLLBACK")
  console.log("This will restore the original subscription structure!")
  console.log("⚠️  All new flat subscription data will be DELETED!")

  // Confirm before proceeding
  console.log("Press Ctrl+C to cancel, or wait 10 seconds to continue...")
  await new Promise((resolve) => setTimeout(resolve, 10000))

  const result = await AdminMigrationService.emergencyRollback()

  if (result.success) {
    const { restoredCount, deletedCount, errors } = result.data!
    console.log(`✅ Rollback completed:`)
    console.log(`  📦 Restored: ${restoredCount} original subscriptions`)
    console.log(`  🗑️  Deleted: ${deletedCount} flat entries`)

    if (errors.length > 0) {
      console.log("⚠️  Rollback warnings:")
      errors.forEach((error: string) => console.log(`  - ${error}`))
    }
  } else {
    console.error("❌ Rollback failed:", (result.error as Error)?.message || result.error)
    process.exit(1)
  }
}

async function performBackupOnly() {
  console.log("📦 Backing up existing subscription data...\n")

  const result = await AdminMigrationService.backupExistingData()

  if (result.success) {
    const { backedUpCount, errors } = result.data!
    console.log(`✅ Backup completed: ${backedUpCount} subscriptions backed up`)

    if (errors.length > 0) {
      console.log("⚠️  Backup warnings:")
      errors.forEach((error: string) => console.log(`  - ${error}`))
    }
  } else {
    console.error("❌ Backup failed:", (result.error as Error)?.message || result.error)
    process.exit(1)
  }
}

async function validateMigration() {
  console.log("🔍 Validating flat subscription structure...\n")

  const result = await AdminMigrationService.validateMigration()

  if (!result.success) {
    console.error("❌ Validation failed:", (result.error as Error)?.message || result.error)
    process.exit(1)
  }

  const { isValid, issues, statistics } = result.data!

  console.log("📊 Migration Statistics:")
  console.log(`  Total users: ${statistics.totalUsers}`)
  console.log(`  Users with subscriptions: ${statistics.usersWithSubscriptions}`)
  console.log(`  Total subscription entries: ${statistics.totalEntries}`)
  console.log(`  Free entries: ${statistics.freeEntries}`)
  console.log(`  Stripe entries: ${statistics.stripeEntries}`)
  console.log(`  Users without free entry: ${statistics.usersWithoutFreeEntry}`)

  if (isValid) {
    console.log("\n✅ Migration validation PASSED")
  } else {
    console.log("\n❌ Migration validation FAILED")
    console.log("\n🚨 Issues found:")
    issues.forEach((issue: string, index: number) => {
      console.log(`  ${index + 1}. ${issue}`)
    })
    process.exit(1)
  }
}

async function migrateSingleUser(userId: string, dryRun: boolean = false) {
  console.log(`${dryRun ? "🔍 [DRY RUN]" : "🔄"} Migrating user: ${userId}\n`)

  if (dryRun) {
    console.log("This is a dry run - no changes will be made")
    console.log("Would migrate subscription data for user:", userId)
    return
  }

  const result = await AdminMigrationService.migrateUserSubscription(userId)

  if (!result.success) {
    console.error("❌ Migration failed:", (result.error as Error)?.message || result.error)
    process.exit(1)
  }

  const { migrated, entriesCreated, reason } = result.data!

  if (migrated) {
    console.log(`✅ User migrated successfully`)
    console.log(`  📝 Entries created: ${entriesCreated}`)
    console.log(`  📋 Reason: ${reason}`)
  } else {
    console.log(`⏭️ User migration skipped: ${reason}`)
  }
}

async function dryRunMigration() {
  console.log("🔍 [DRY RUN] Full migration preview\n")
  console.log("This is a dry run - no changes will be made")

  console.log("Would perform the following actions:")
  console.log("1. 📦 Backup existing subscription data")
  console.log("2. 🔄 Convert subscription documents to flat entries")
  console.log("3. ➕ Ensure all users have free subscription entries")
  console.log("4. 🗑️  Delete original subscription documents")
  console.log("5. 🔍 Validate migration results")

  console.log("\nTo perform the actual migration, run without --dry-run flag")
}

async function runFullMigration() {
  console.log("🚀 Starting full flat subscription migration...\n")

  // Confirm before proceeding
  console.log("⚠️  This will completely restructure your subscription data!")
  console.log("⚠️  Make sure you have a database backup!")
  console.log("Press Ctrl+C to cancel, or wait 5 seconds to continue...")

  await new Promise((resolve) => setTimeout(resolve, 5000))

  console.log("\n🔄 Starting migration...")

  const migrationResult = await AdminMigrationService.migrateAllUsers()

  if (!migrationResult.success) {
    console.error(
      "❌ Migration failed:",
      (migrationResult.error as Error)?.message || migrationResult.error
    )
    process.exit(1)
  }

  const { totalProcessed, successfulMigrations, totalEntriesCreated, errors } =
    migrationResult.data!

  console.log(`\n✅ Migration completed:`)
  console.log(`  📊 Total processed: ${totalProcessed}`)
  console.log(`  ✅ Successful: ${successfulMigrations}`)
  console.log(`  📝 Entries created: ${totalEntriesCreated}`)
  console.log(`  ❌ Errors: ${errors.length}`)

  if (errors.length > 0) {
    console.log("\n🚨 Migration errors:")
    errors.forEach((error: string, index: number) => {
      console.log(`  ${index + 1}. ${error}`)
    })
  }

  console.log("\n🔍 Running validation...")
  await validateMigration()

  console.log("\n🎉 Migration completed successfully!")
  console.log("\nNext steps:")
  console.log("1. Test the application with the new subscription system")
  console.log("2. Monitor for any issues")
  console.log("3. Update your application code to use FlatSubscriptionService")
  console.log("4. Remove backup data after confirming everything works")
}

// Handle uncaught errors
process.on("uncaughtException", (error) => {
  console.error("💥 Uncaught exception:", error)
  process.exit(1)
})

process.on("unhandledRejection", (reason, promise) => {
  console.error("💥 Unhandled rejection at:", promise, "reason:", reason)
  process.exit(1)
})

// Run the script
main().catch((error) => {
  console.error("💥 Script failed:", error)
  process.exit(1)
})
