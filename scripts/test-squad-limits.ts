/**
 * Test script to verify squad limits with perk system
 *
 * This script:
 * 1. Checks a user's current squad count
 * 2. Checks their subscription status
 * 3. Checks their active perks
 * 4. Calculates their enhanced squad limits
 * 5. Determines if they can create more squads
 *
 * Usage:
 * npx tsx scripts/test-squad-limits.ts <userId>
 * npx tsx scripts/test-squad-limits.ts <userId> .env.production
 */

import { initializeApp, cert, getApps } from "firebase-admin/app"
import { getFirestore } from "firebase-admin/firestore"
import { config } from "dotenv"
import { resolve } from "path"

// Load environment variables
const envFile = process.env.ENV_FILE || process.argv[3] || ".env.local"
const envPath = resolve(process.cwd(), envFile)

console.log(`Loading environment from: ${envPath}`)
config({ path: envPath })

// Validate required environment variables
if (!process.env.FIREBASE_SERVICE_ACCOUNT_KEY) {
  console.error("❌ Missing required environment variable: FIREBASE_SERVICE_ACCOUNT_KEY")
  process.exit(1)
}

// Parse the service account key
let serviceAccount
try {
  serviceAccount = JSON.parse(process.env.FIREBASE_SERVICE_ACCOUNT_KEY!)
} catch (error) {
  console.error("❌ Failed to parse FIREBASE_SERVICE_ACCOUNT_KEY as JSON:", error)
  process.exit(1)
}

// Initialize Firebase Admin
if (getApps().length === 0) {
  initializeApp({
    credential: cert(serviceAccount),
  })
}

const db = getFirestore()

/**
 * Get user's current squad count
 */
async function getUserSquadCount(userId: string): Promise<number> {
  try {
    const squadsSnapshot = await db.collection("squads").where("leaderId", "==", userId).get()

    return squadsSnapshot.size
  } catch (error) {
    console.error("Error getting user squad count:", error)
    return 0
  }
}

/**
 * Get user's subscription status
 */
async function getUserSubscriptionStatus(
  userId: string
): Promise<{ isSubscribed: boolean; plan: string }> {
  try {
    const userDoc = await db.collection("users").doc(userId).get()
    const userData = userDoc.data()

    const isSubscribed =
      userData?.subscriptionStatus === "active" || userData?.subscriptionStatus === "trialing"
    const plan = isSubscribed ? "pro" : "free"

    return { isSubscribed, plan }
  } catch (error) {
    console.error("Error getting user subscription status:", error)
    return { isSubscribed: false, plan: "free" }
  }
}

/**
 * Get user's active perks
 */
async function getUserActivePerks(userId: string): Promise<any[]> {
  try {
    const perksSnapshot = await db.collection("users").doc(userId).collection("perks").get()

    const activePerks = []
    for (const perkDoc of perksSnapshot.docs) {
      const perkData = perkDoc.data()

      // Only include unlocked or applied perks
      if (perkData.status === "expired") continue

      // Check if perk has expired (for non-permanent perks)
      if (perkData.expiresAt && perkData.expiresAt.toMillis() < Date.now()) {
        continue
      }

      activePerks.push({
        id: perkDoc.id,
        ...perkData,
      })
    }

    return activePerks
  } catch (error) {
    console.error("Error getting user active perks:", error)
    return []
  }
}

/**
 * Calculate enhanced squad limits
 */
function calculateEnhancedSquadLimits(isSubscribed: boolean, activePerks: any[]): number {
  // Base limits
  const baseLimit = isSubscribed ? 5 : 1 // PRO: 5, FREE: 1

  // Add perk enhancements
  let additionalSquads = 0
  for (const perk of activePerks) {
    const { perkDetails } = perk
    if (perkDetails?.tags?.includes("squad") && perkDetails?.perkValue?.maxSquads) {
      additionalSquads += perkDetails.perkValue.maxSquads
    }
  }

  return baseLimit + additionalSquads
}

/**
 * Main test function
 */
async function testSquadLimits(userId: string) {
  console.log("🧪 Testing squad limits for user:", userId)
  console.log("=".repeat(60))

  try {
    // Get user data
    const userDoc = await db.collection("users").doc(userId).get()
    if (!userDoc.exists) {
      console.error("❌ User not found:", userId)
      return
    }

    const userData = userDoc.data()
    console.log("👤 User:", userData?.email || "No email")

    // Get current squad count
    const currentSquadCount = await getUserSquadCount(userId)
    console.log("📊 Current squad count:", currentSquadCount)

    // Get subscription status
    const { isSubscribed, plan } = await getUserSubscriptionStatus(userId)
    console.log("💳 Subscription:", plan.toUpperCase(), isSubscribed ? "(Active)" : "(Inactive)")

    // Get active perks
    const activePerks = await getUserActivePerks(userId)
    console.log("🎁 Active perks:", activePerks.length)

    if (activePerks.length > 0) {
      console.log("   Perk details:")
      for (const perk of activePerks) {
        const { perkDetails } = perk
        console.log(
          `   - ${perkDetails?.name || perk.id}: ${perkDetails?.description || "No description"}`
        )
        if (perkDetails?.tags?.includes("squad")) {
          console.log(`     → Adds ${perkDetails?.perkValue?.maxSquads || 0} squad(s)`)
        }
      }
    }

    // Calculate enhanced limits
    const enhancedLimit = calculateEnhancedSquadLimits(isSubscribed, activePerks)
    console.log("🎯 Enhanced squad limit:", enhancedLimit)

    // Check if user can create more squads
    const canCreateMore = currentSquadCount < enhancedLimit
    console.log("✅ Can create more squads:", canCreateMore ? "YES" : "NO")

    if (canCreateMore) {
      const remaining = enhancedLimit - currentSquadCount
      console.log(`   → Can create ${remaining} more squad(s)`)
    } else {
      console.log("   → Limit reached")
    }

    console.log("\n📋 Summary:")
    console.log(`   Base limit (${plan}): ${isSubscribed ? 5 : 1}`)
    console.log(`   Perk bonuses: +${enhancedLimit - (isSubscribed ? 5 : 1)}`)
    console.log(`   Total limit: ${enhancedLimit}`)
    console.log(`   Current usage: ${currentSquadCount}/${enhancedLimit}`)
  } catch (error) {
    console.error("❌ Error testing squad limits:", error)
  }
}

/**
 * Main function
 */
async function main() {
  const userId = process.argv[2]

  if (!userId) {
    console.error("❌ Usage: npx tsx scripts/test-squad-limits.ts <userId> [envFile]")
    console.error("Example: npx tsx scripts/test-squad-limits.ts abc123")
    process.exit(1)
  }

  await testSquadLimits(userId)
}

// Run the script if executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  main()
    .then(() => {
      console.log("\n🎉 Test completed!")
      process.exit(0)
    })
    .catch((error) => {
      console.error("\n💥 Test failed:", error)
      process.exit(1)
    })
}

export { testSquadLimits }
