#!/usr/bin/env tsx

/**
 * Debug script to examine current subscription perks and their status
 */

import { config } from "dotenv"
import { resolve } from "path"
import { initializeApp, cert, getApps } from "firebase-admin/app"
import { getFirestore } from "firebase-admin/firestore"

// Parse command line arguments
const args = process.argv.slice(2)
const envFile = args.find(arg => !arg.startsWith('--')) || '.env.local'

// Initialize Firebase Admin SDK
async function initializeFirebase() {
  const envPath = resolve(process.cwd(), envFile)
  console.log(`Loading environment variables from: ${envPath}`)
  config({ path: envPath })

  if (!getApps().length) {
    try {
      if (!process.env.FIREBASE_SERVICE_ACCOUNT_KEY) {
        throw new Error("FIREBASE_SERVICE_ACCOUNT_KEY environment variable is not set")
      }

      const serviceAccount = JSON.parse(process.env.FIREBASE_SERVICE_ACCOUNT_KEY)

      initializeApp({
        credential: cert(serviceAccount),
        databaseURL: `https://${process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID}.firebaseio.com`,
      })

      console.log('✅ Firebase Admin SDK initialized successfully')
    } catch (error) {
      console.error("❌ Firebase admin initialization error:", error)
      process.exit(1)
    }
  }

  return getFirestore()
}

async function debugSubscriptionPerks() {
  const db = getFirestore()
  
  console.log('\n🔍 Examining subscription perks across all users...\n')
  
  // Get all users
  const usersSnapshot = await db.collection('users').get()
  console.log(`📊 Total users: ${usersSnapshot.size}`)
  
  let totalPerks = 0
  let subscriptionPerks = 0
  let unlockedSubscriptionPerks = 0
  let appliedSubscriptionPerks = 0
  let expiredSubscriptionPerks = 0
  
  const usersWithSubscriptionPerks: any[] = []
  
  console.log('\n📋 User Perk Analysis:')
  console.log('='.repeat(80))
  
  for (const userDoc of usersSnapshot.docs) {
    const userId = userDoc.id
    const userData = userDoc.data()
    
    // Get user's perks
    const perksSnapshot = await db.collection('users').doc(userId).collection('perks').get()
    
    if (perksSnapshot.size > 0) {
      console.log(`\n👤 User: ${userId} (${userData.email || 'No email'})`)
      console.log(`   Total perks: ${perksSnapshot.size}`)
      
      const userSubscriptionPerks: any[] = []
      
      for (const perkDoc of perksSnapshot.docs) {
        const perkData = perkDoc.data()
        totalPerks++
        
        // Check if it's a subscription perk
        if (perkData.perkDetails?.perkType === 'subscription') {
          subscriptionPerks++
          userSubscriptionPerks.push({
            perkId: perkDoc.id,
            ...perkData
          })
          
          console.log(`   🎯 SUBSCRIPTION PERK: ${perkDoc.id}`)
          console.log(`      Name: ${perkData.perkDetails?.name || 'Unknown'}`)
          console.log(`      Status: ${perkData.status}`)
          console.log(`      Duration: ${perkData.perkDetails?.perkValue?.duration || 'Unknown'} days`)
          console.log(`      Unlocked At: ${perkData.unlockedAt ? new Date(perkData.unlockedAt.seconds * 1000).toISOString() : 'N/A'}`)
          console.log(`      Applied At: ${perkData.appliedAt ? new Date(perkData.appliedAt.seconds * 1000).toISOString() : 'N/A'}`)
          console.log(`      Expires At: ${perkData.expiresAt ? new Date(perkData.expiresAt.seconds * 1000).toISOString() : 'N/A'}`)
          
          // Count by status
          if (perkData.status === 'unlocked') {
            unlockedSubscriptionPerks++
            console.log(`      🔓 NEEDS MIGRATION TO FLAT SUBSCRIPTION`)
          } else if (perkData.status === 'applied') {
            appliedSubscriptionPerks++
            console.log(`      ✅ Already applied (check if in flat subscription)`)
          } else if (perkData.status === 'expired') {
            expiredSubscriptionPerks++
            console.log(`      ⏰ Expired`)
          }
        } else {
          console.log(`   📦 Other perk: ${perkDoc.id} (${perkData.perkDetails?.perkType || 'unknown'}) - ${perkData.status}`)
        }
      }
      
      if (userSubscriptionPerks.length > 0) {
        usersWithSubscriptionPerks.push({
          userId,
          email: userData.email,
          subscriptionPerks: userSubscriptionPerks
        })
      }
    }
  }
  
  console.log('\n📊 Summary:')
  console.log('='.repeat(50))
  console.log(`Total perks across all users: ${totalPerks}`)
  console.log(`Subscription perks: ${subscriptionPerks}`)
  console.log(`  - Unlocked (need migration): ${unlockedSubscriptionPerks}`)
  console.log(`  - Applied (check flat subscription): ${appliedSubscriptionPerks}`)
  console.log(`  - Expired: ${expiredSubscriptionPerks}`)
  console.log(`Users with subscription perks: ${usersWithSubscriptionPerks.length}`)
  
  // Check current flat subscription entries for perk source
  console.log('\n🔍 Checking existing perk entries in flat subscription system...')
  const subscriptionsSnapshot = await db.collection('userSubscriptions').get()
  
  let perkEntriesInFlat = 0
  const perkEntriesDetails: any[] = []
  
  for (const subDoc of subscriptionsSnapshot.docs) {
    const subData = subDoc.data()
    if (subData.source === 'perk') {
      perkEntriesInFlat++
      perkEntriesDetails.push({
        docId: subDoc.id,
        userId: subData.userId,
        status: subData.status,
        perkId: subData.subscriptionData?.perkId,
        appliedAt: subData.subscriptionData?.appliedAt,
        duration: subData.subscriptionData?.duration,
        startDate: subData.startDate,
        endDate: subData.endDate
      })
    }
  }
  
  console.log(`Perk entries in flat subscription: ${perkEntriesInFlat}`)
  
  if (perkEntriesDetails.length > 0) {
    console.log('\nExisting perk entries in flat subscription:')
    perkEntriesDetails.forEach((entry, index) => {
      console.log(`  ${index + 1}. User: ${entry.userId}`)
      console.log(`     Perk ID: ${entry.perkId}`)
      console.log(`     Status: ${entry.status}`)
      console.log(`     Duration: ${entry.duration} days`)
    })
  }
  
  // Recommendations
  console.log('\n💡 Migration Recommendations:')
  console.log('='.repeat(50))
  
  if (unlockedSubscriptionPerks > 0) {
    console.log(`🔄 ${unlockedSubscriptionPerks} unlocked subscription perks need to be applied and migrated to flat subscription`)
  }
  
  if (appliedSubscriptionPerks > 0) {
    console.log(`🔍 ${appliedSubscriptionPerks} applied subscription perks should be checked - they might already be in flat subscription or need migration`)
  }
  
  if (usersWithSubscriptionPerks.length === 0) {
    console.log(`✅ No users have subscription perks - no migration needed`)
  } else {
    console.log(`📋 ${usersWithSubscriptionPerks.length} users have subscription perks that may need attention`)
  }
}

async function main() {
  try {
    await initializeFirebase()
    await debugSubscriptionPerks()
  } catch (error) {
    console.error('❌ Debug failed:', error)
    process.exit(1)
  }
}

main().catch((error) => {
  console.error('💥 Script failed:', error)
  process.exit(1)
})
