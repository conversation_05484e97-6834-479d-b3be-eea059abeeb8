#!/usr/bin/env tsx

/**
 * End-to-End Test Script for Flat Subscription System
 * 
 * This script validates the entire flat subscription architecture including:
 * - Single-user queries
 * - Multi-user queries (squad member badges)
 * - Precedence system
 * - Pause/resume logic with activeDays
 * - Migration functionality
 * 
 * Usage:
 *   npm run test:flat-subscriptions [options]
 * 
 * Options:
 *   --env <path>       Path to environment file (default: .env.local)
 *   --cleanup          Clean up test data after running
 *   --verbose          Show detailed output
 */

import { config } from 'dotenv'
import { Command } from 'commander'
import { FlatSubscriptionService } from '../lib/domains/user-subscription/flat-subscription.service'
import { CleanMigrationService } from '../lib/domains/user-subscription/clean-migration.service'
import { FlatSubscriptionCronService } from '../lib/domains/user-subscription/flat-subscription-cron.service'
import {
  StripeSubscriptionData,
  PerkSubscriptionData,
  GiveawaySubscriptionData
} from '../lib/domains/user-subscription/user-subscription.types'

const program = new Command()

program
  .name('test-flat-subscriptions')
  .description('End-to-end test for flat subscription system')
  .option('--env <path>', 'Path to environment file', '.env.local')
  .option('--cleanup', 'Clean up test data after running')
  .option('--verbose', 'Show detailed output')

program.parse()

const options = program.opts()

// Test data
const TEST_USERS = [
  'test-user-1',
  'test-user-2', 
  'test-user-3',
  'test-user-4',
  'test-user-5'
]

const TEST_SUBSCRIPTION_IDS: string[] = []

async function main() {
  try {
    // Load environment variables
    config({ path: options.env })
    
    console.log('🧪 Flat Subscription System End-to-End Tests')
    console.log('==============================================')
    
    await runAllTests()
    
    if (options.cleanup) {
      await cleanupTestData()
    }
    
    console.log('\n✅ All tests completed successfully!')
    
  } catch (error) {
    console.error('❌ Tests failed:', error)
    
    if (options.cleanup) {
      await cleanupTestData()
    }
    
    process.exit(1)
  }
}

async function runAllTests() {
  console.log('\n📋 Running test suite...\n')
  
  await testFreeSubscriptionCreation()
  await testSingleUserQueries()
  await testMultiUserQueries()
  await testPrecedenceSystem()
  await testPauseResumeLogic()
  await testSubscriptionLimits()
  await testFeatureAccess()
  await testCronJobs()
  await testMigrationValidation()
}

async function testFreeSubscriptionCreation() {
  console.log('🆓 Testing free subscription creation...')
  
  for (const userId of TEST_USERS) {
    const result = await FlatSubscriptionService.ensureFreeSubscription(userId)
    
    if (!result.success) {
      throw new Error(`Failed to create free subscription for ${userId}: ${result.error?.message}`)
    }
    
    if (options.verbose) {
      console.log(`  ✅ Free subscription created for ${userId}`)
    }
  }
  
  console.log('✅ Free subscription creation test passed')
}

async function testSingleUserQueries() {
  console.log('👤 Testing single-user subscription queries...')
  
  const userId = TEST_USERS[0]
  
  // Test getCurrentSubscription
  const currentSubscription = await FlatSubscriptionService.getCurrentSubscription(userId)
  if (!currentSubscription || currentSubscription.source !== 'free') {
    throw new Error('Expected free subscription as current subscription')
  }
  
  // Test getUserSubscriptions
  const allSubscriptions = await FlatSubscriptionService.getUserSubscriptions(userId)
  if (allSubscriptions.length !== 1 || allSubscriptions[0].source !== 'free') {
    throw new Error('Expected exactly one free subscription')
  }
  
  // Test getSubscriptionSummary
  const summary = await FlatSubscriptionService.getSubscriptionSummary(userId)
  if (summary.isSubscribed || summary.currentSource !== 'free') {
    throw new Error('Expected free user summary')
  }
  
  console.log('✅ Single-user queries test passed')
}

async function testMultiUserQueries() {
  console.log('👥 Testing multi-user queries (squad member badges)...')
  
  // Test with all test users
  const subscriptions = await FlatSubscriptionService.getMultiUserSubscriptions(TEST_USERS)
  
  if (subscriptions.length !== TEST_USERS.length) {
    throw new Error(`Expected ${TEST_USERS.length} subscriptions, got ${subscriptions.length}`)
  }
  
  // Verify each user has a subscription
  for (const userId of TEST_USERS) {
    const userSubscription = subscriptions.find(sub => sub.userId === userId)
    if (!userSubscription) {
      throw new Error(`No subscription found for user ${userId}`)
    }
  }
  
  // Test with empty array
  const emptyResult = await FlatSubscriptionService.getMultiUserSubscriptions([])
  if (emptyResult.length !== 0) {
    throw new Error('Expected empty result for empty user array')
  }
  
  console.log('✅ Multi-user queries test passed')
}

async function testPrecedenceSystem() {
  console.log('🏆 Testing subscription precedence system...')
  
  const userId = TEST_USERS[1]
  
  // Add Stripe subscription (precedence 3)
  const stripeData: StripeSubscriptionData = {
    customerId: 'test-cus-123',
    subscriptionId: 'test-sub-123',
    subscriptionStatus: 'active',
    subscriptionPlan: 'monthly',
    currentPeriodEnd: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000) as any
  }
  
  const stripeResult = await FlatSubscriptionService.addStripeSubscription(userId, stripeData)
  if (!stripeResult.success) {
    throw new Error(`Failed to add Stripe subscription: ${stripeResult.error?.message}`)
  }
  TEST_SUBSCRIPTION_IDS.push(stripeResult.id!)
  
  // Verify Stripe is now current
  let currentSubscription = await FlatSubscriptionService.getCurrentSubscription(userId)
  if (!currentSubscription || currentSubscription.source !== 'stripe') {
    throw new Error('Expected Stripe subscription to be current')
  }
  
  // Add perk subscription (precedence 1 - higher)
  const perkData: PerkSubscriptionData = {
    perkId: 'test-perk-123',
    perkName: 'Test Perk',
    grantedBy: 'test-admin',
    duration: 7
  }
  
  const perkResult = await FlatSubscriptionService.addPerkSubscription(userId, perkData)
  if (!perkResult.success) {
    throw new Error(`Failed to add perk subscription: ${perkResult.error?.message}`)
  }
  TEST_SUBSCRIPTION_IDS.push(perkResult.id!)
  
  // Verify perk is now current (higher precedence)
  currentSubscription = await FlatSubscriptionService.getCurrentSubscription(userId)
  if (!currentSubscription || currentSubscription.source !== 'perk') {
    throw new Error('Expected perk subscription to be current (higher precedence)')
  }
  
  // Verify Stripe subscription was paused
  const allSubscriptions = await FlatSubscriptionService.getUserSubscriptions(userId)
  const stripeSubscription = allSubscriptions.find(sub => sub.source === 'stripe')
  if (!stripeSubscription || stripeSubscription.status !== 'paused') {
    throw new Error('Expected Stripe subscription to be paused')
  }
  
  console.log('✅ Precedence system test passed')
}

async function testPauseResumeLogic() {
  console.log('⏸️ Testing pause/resume logic with activeDays...')
  
  const userId = TEST_USERS[2]
  
  // Add Stripe subscription
  const stripeData: StripeSubscriptionData = {
    customerId: 'test-cus-456',
    subscriptionId: 'test-sub-456',
    subscriptionStatus: 'active',
    subscriptionPlan: 'yearly',
    currentPeriodEnd: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000) as any
  }
  
  const stripeResult = await FlatSubscriptionService.addStripeSubscription(userId, stripeData)
  if (!stripeResult.success) {
    throw new Error(`Failed to add Stripe subscription: ${stripeResult.error?.message}`)
  }
  TEST_SUBSCRIPTION_IDS.push(stripeResult.id!)
  
  // Wait a moment to ensure time difference
  await new Promise(resolve => setTimeout(resolve, 100))
  
  // Add giveaway subscription (precedence 2 - higher than Stripe)
  const giveawayData: GiveawaySubscriptionData = {
    giveawayId: 'test-giveaway-123',
    giveawayName: 'Test Giveaway',
    duration: 3
  }
  
  const giveawayResult = await FlatSubscriptionService.addGiveawaySubscription(userId, giveawayData)
  if (!giveawayResult.success) {
    throw new Error(`Failed to add giveaway subscription: ${giveawayResult.error?.message}`)
  }
  TEST_SUBSCRIPTION_IDS.push(giveawayResult.id!)
  
  // Verify Stripe was paused with activeDays
  const allSubscriptions = await FlatSubscriptionService.getUserSubscriptions(userId)
  const pausedStripe = allSubscriptions.find(sub => sub.source === 'stripe')
  
  if (!pausedStripe || pausedStripe.status !== 'paused') {
    throw new Error('Expected Stripe subscription to be paused')
  }
  
  if (!pausedStripe.activeDays || !pausedStripe.pausedAt) {
    throw new Error('Expected activeDays and pausedAt to be set on paused subscription')
  }
  
  console.log('✅ Pause/resume logic test passed')
}

async function testSubscriptionLimits() {
  console.log('📊 Testing subscription limits...')
  
  // Test free user limits
  const freeUserId = TEST_USERS[3]
  const freeLimits = await FlatSubscriptionService.getSubscriptionLimits(freeUserId)
  
  if (freeLimits.maxSquads !== 1 || freeLimits.hasTripChat !== false) {
    throw new Error('Incorrect free user limits')
  }
  
  // Test pro user limits (user with Stripe subscription)
  const proUserId = TEST_USERS[1] // Has perk subscription (pro)
  const proLimits = await FlatSubscriptionService.getSubscriptionLimits(proUserId)
  
  if (proLimits.maxSquads !== 5 || proLimits.hasTripChat !== true) {
    throw new Error('Incorrect pro user limits')
  }
  
  console.log('✅ Subscription limits test passed')
}

async function testFeatureAccess() {
  console.log('🔐 Testing feature access...')
  
  // Test free user access
  const freeUserId = TEST_USERS[3]
  const freeTripChat = await FlatSubscriptionService.hasFeatureAccess(freeUserId, 'trip_chat')
  const freeUnlimitedAI = await FlatSubscriptionService.hasFeatureAccess(freeUserId, 'unlimited_ai')
  
  if (freeTripChat || freeUnlimitedAI) {
    throw new Error('Free user should not have premium feature access')
  }
  
  // Test pro user access
  const proUserId = TEST_USERS[1] // Has perk subscription
  const proTripChat = await FlatSubscriptionService.hasFeatureAccess(proUserId, 'trip_chat')
  const proUnlimitedAI = await FlatSubscriptionService.hasFeatureAccess(proUserId, 'unlimited_ai')
  
  if (!proTripChat || !proUnlimitedAI) {
    throw new Error('Pro user should have premium feature access')
  }
  
  console.log('✅ Feature access test passed')
}

async function testCronJobs() {
  console.log('⏰ Testing cron job functionality...')
  
  // Test health check
  const healthResult = await FlatSubscriptionCronService.performHealthCheck()
  if (!healthResult.success) {
    throw new Error(`Health check failed: ${healthResult.error?.message}`)
  }
  
  if (options.verbose) {
    console.log(`  📊 Health check: ${healthResult.data?.systemHealth}`)
    console.log(`  👥 Total users: ${healthResult.data?.totalUsers}`)
    console.log(`  ⚠️  Users with issues: ${healthResult.data?.usersWithIssues}`)
  }
  
  console.log('✅ Cron jobs test passed')
}

async function testMigrationValidation() {
  console.log('🔄 Testing migration validation...')
  
  const validationResult = await CleanMigrationService.validateMigration()
  if (!validationResult.success) {
    throw new Error(`Migration validation failed: ${validationResult.error?.message}`)
  }
  
  if (options.verbose) {
    const stats = validationResult.data?.statistics
    console.log(`  📊 Total users: ${stats?.totalUsers}`)
    console.log(`  📝 Total entries: ${stats?.totalEntries}`)
    console.log(`  🆓 Free entries: ${stats?.freeEntries}`)
    console.log(`  💳 Stripe entries: ${stats?.stripeEntries}`)
  }
  
  if (!validationResult.data?.isValid) {
    console.log('⚠️  Migration validation found issues:')
    validationResult.data?.issues.forEach((issue, index) => {
      console.log(`  ${index + 1}. ${issue}`)
    })
  }
  
  console.log('✅ Migration validation test passed')
}

async function cleanupTestData() {
  console.log('\n🧹 Cleaning up test data...')
  
  try {
    // Delete test subscription entries
    for (const subscriptionId of TEST_SUBSCRIPTION_IDS) {
      await FlatSubscriptionService.deleteSubscriptionEntry(subscriptionId)
    }
    
    // Delete test user free subscriptions
    for (const userId of TEST_USERS) {
      const userSubscriptions = await FlatSubscriptionService.getUserSubscriptions(userId)
      for (const subscription of userSubscriptions) {
        await FlatSubscriptionService.deleteSubscriptionEntry(subscription.id)
      }
    }
    
    console.log('✅ Test data cleanup completed')
  } catch (error) {
    console.error('⚠️  Error during cleanup:', error)
  }
}

// Handle uncaught errors
process.on('uncaughtException', async (error) => {
  console.error('💥 Uncaught exception:', error)
  
  if (options.cleanup) {
    await cleanupTestData()
  }
  
  process.exit(1)
})

process.on('unhandledRejection', async (reason, promise) => {
  console.error('💥 Unhandled rejection at:', promise, 'reason:', reason)
  
  if (options.cleanup) {
    await cleanupTestData()
  }
  
  process.exit(1)
})

// Run the tests
main().catch(async (error) => {
  console.error('💥 Test suite failed:', error)
  
  if (options.cleanup) {
    await cleanupTestData()
  }
  
  process.exit(1)
})
