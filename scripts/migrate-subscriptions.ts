#!/usr/bin/env tsx

/**
 * Subscription Migration Script
 * 
 * This script migrates existing single subscription documents to the new multi-entry system.
 * 
 * Usage:
 *   npm run migrate:subscriptions [options]
 * 
 * Options:
 *   --dry-run          Show what would be migrated without making changes
 *   --user-id <id>     Migrate only a specific user
 *   --validate-only    Only run validation, don't migrate
 *   --rollback <id>    Rollback migration for a specific user
 *   --status           Show migration status
 *   --env <path>       Path to environment file (default: .env.local)
 */

import { config } from 'dotenv'
import { Command } from 'commander'
import { SubscriptionMigrationService } from '../lib/domains/user-subscription/subscription-migration.service'

// Load environment variables
const program = new Command()

program
  .name('migrate-subscriptions')
  .description('Migrate subscriptions to multi-entry system')
  .option('--dry-run', 'Show what would be migrated without making changes')
  .option('--user-id <id>', 'Migrate only a specific user')
  .option('--validate-only', 'Only run validation, don\'t migrate')
  .option('--rollback <id>', 'Rollback migration for a specific user')
  .option('--status', 'Show migration status')
  .option('--env <path>', 'Path to environment file', '.env.local')

program.parse()

const options = program.opts()

async function main() {
  try {
    // Load environment variables
    config({ path: options.env })
    
    console.log('🚀 Subscription Migration Tool')
    console.log('================================')
    
    if (options.status) {
      await showMigrationStatus()
      return
    }
    
    if (options.rollback) {
      await rollbackUserMigration(options.rollback)
      return
    }
    
    if (options.validateOnly) {
      await validateMigration()
      return
    }
    
    if (options.userId) {
      await migrateSingleUser(options.userId, options.dryRun)
      return
    }
    
    if (options.dryRun) {
      await dryRunMigration()
      return
    }
    
    // Full migration
    await runFullMigration()
    
  } catch (error) {
    console.error('❌ Migration failed:', error)
    process.exit(1)
  }
}

async function showMigrationStatus() {
  console.log('📊 Checking migration status...\n')
  
  const status = await SubscriptionMigrationService.getMigrationStatus()
  
  if (status.isCompleted) {
    console.log('✅ Migration completed')
    console.log('Summary:', status.summary)
    console.log(`\n📈 User migrations: ${status.userStatuses.length}`)
    
    const successful = status.userStatuses.filter(u => u.migrated).length
    const failed = status.userStatuses.filter(u => !u.migrated).length
    
    console.log(`  ✅ Successful: ${successful}`)
    console.log(`  ❌ Failed: ${failed}`)
  } else {
    console.log('⏳ Migration not completed')
    console.log(`📈 Partial migrations: ${status.userStatuses.length}`)
  }
}

async function rollbackUserMigration(userId: string) {
  console.log(`🔄 Rolling back migration for user: ${userId}\n`)
  
  const result = await SubscriptionMigrationService.rollbackUserMigration(userId)
  
  if (result.success) {
    console.log('✅ Rollback completed successfully')
  } else {
    console.error('❌ Rollback failed:', result.error?.message)
    process.exit(1)
  }
}

async function validateMigration() {
  console.log('🔍 Validating migration...\n')
  
  const result = await SubscriptionMigrationService.validateMigration()
  
  if (!result.success) {
    console.error('❌ Validation failed:', result.error?.message)
    process.exit(1)
  }
  
  const { isValid, issues, statistics } = result.data!
  
  console.log('📊 Migration Statistics:')
  console.log(`  Legacy subscriptions: ${statistics.legacySubscriptions}`)
  console.log(`  New entries: ${statistics.newEntries}`)
  console.log(`  Migrated users: ${statistics.migratedUsers}`)
  
  if (isValid) {
    console.log('\n✅ Migration validation PASSED')
  } else {
    console.log('\n❌ Migration validation FAILED')
    console.log('\n🚨 Issues found:')
    issues.forEach((issue, index) => {
      console.log(`  ${index + 1}. ${issue}`)
    })
    process.exit(1)
  }
}

async function migrateSingleUser(userId: string, dryRun: boolean = false) {
  console.log(`${dryRun ? '🔍 [DRY RUN]' : '🔄'} Migrating user: ${userId}\n`)
  
  if (dryRun) {
    console.log('This is a dry run - no changes will be made')
    // In a real implementation, we'd show what would be migrated
    console.log('Would migrate subscription data for user:', userId)
    return
  }
  
  const result = await SubscriptionMigrationService.migrateUserSubscription(userId)
  
  if (!result.success) {
    console.error('❌ Migration failed:', result.error?.message)
    process.exit(1)
  }
  
  const { migrated, entryId, reason } = result.data!
  
  if (migrated) {
    console.log(`✅ User migrated successfully (Entry ID: ${entryId})`)
  } else {
    console.log(`⏭️ User migration skipped: ${reason}`)
  }
}

async function dryRunMigration() {
  console.log('🔍 [DRY RUN] Full migration preview\n')
  console.log('This is a dry run - no changes will be made')
  
  // In a real implementation, we'd analyze what would be migrated
  console.log('Would perform the following actions:')
  console.log('1. Backup existing subscription data')
  console.log('2. Convert single subscriptions to multi-entry format')
  console.log('3. Validate migration results')
  console.log('4. Generate migration report')
  
  console.log('\nTo perform the actual migration, run without --dry-run flag')
}

async function runFullMigration() {
  console.log('🚀 Starting full subscription migration...\n')
  
  // Confirm before proceeding
  console.log('⚠️  This will modify your database. Make sure you have a backup!')
  console.log('Press Ctrl+C to cancel, or wait 5 seconds to continue...')
  
  await new Promise(resolve => setTimeout(resolve, 5000))
  
  console.log('\n📦 Step 1: Backing up existing data...')
  const backupResult = await SubscriptionMigrationService.backupExistingSubscriptions()
  
  if (!backupResult.success) {
    console.error('❌ Backup failed:', backupResult.error?.message)
    process.exit(1)
  }
  
  console.log(`✅ Backup completed: ${backupResult.data!.backedUpCount} subscriptions backed up`)
  
  if (backupResult.data!.errors.length > 0) {
    console.log('⚠️  Backup warnings:')
    backupResult.data!.errors.forEach(error => console.log(`  - ${error}`))
  }
  
  console.log('\n🔄 Step 2: Migrating subscriptions...')
  const migrationResult = await SubscriptionMigrationService.migrateAllSubscriptions()
  
  if (!migrationResult.success) {
    console.error('❌ Migration failed:', migrationResult.error?.message)
    process.exit(1)
  }
  
  const { totalProcessed, successfulMigrations, skippedMigrations, errors } = migrationResult.data!
  
  console.log(`✅ Migration completed:`)
  console.log(`  📊 Total processed: ${totalProcessed}`)
  console.log(`  ✅ Successful: ${successfulMigrations}`)
  console.log(`  ⏭️  Skipped: ${skippedMigrations}`)
  console.log(`  ❌ Errors: ${errors.length}`)
  
  if (errors.length > 0) {
    console.log('\n🚨 Migration errors:')
    errors.forEach((error, index) => {
      console.log(`  ${index + 1}. ${error}`)
    })
  }
  
  console.log('\n🔍 Step 3: Validating migration...')
  await validateMigration()
  
  console.log('\n🎉 Migration completed successfully!')
  console.log('\nNext steps:')
  console.log('1. Test the application with the new subscription system')
  console.log('2. Monitor for any issues')
  console.log('3. After 30 days, run cleanup script to remove migration artifacts')
}

// Handle uncaught errors
process.on('uncaughtException', (error) => {
  console.error('💥 Uncaught exception:', error)
  process.exit(1)
})

process.on('unhandledRejection', (reason, promise) => {
  console.error('💥 Unhandled rejection at:', promise, 'reason:', reason)
  process.exit(1)
})

// Run the script
main().catch((error) => {
  console.error('💥 Script failed:', error)
  process.exit(1)
})
