#!/usr/bin/env tsx

/**
 * Fix the remaining user with old structure
 */

import { config } from "dotenv"
import { resolve } from "path"
import { initializeApp, cert, getApps } from "firebase-admin/app"
import { getFirestore } from "firebase-admin/firestore"
import { AdminMigrationService } from "../lib/domains/user-subscription/admin-migration.service"

// Initialize Firebase Admin SDK
async function initializeFirebase() {
  const envPath = resolve(process.cwd(), '.env.local')
  console.log(`Loading environment variables from: ${envPath}`)
  config({ path: envPath })

  if (!getApps().length) {
    try {
      if (!process.env.FIREBASE_SERVICE_ACCOUNT_KEY) {
        throw new Error("FIREBASE_SERVICE_ACCOUNT_KEY environment variable is not set")
      }

      const serviceAccount = JSON.parse(process.env.FIREBASE_SERVICE_ACCOUNT_KEY)

      initializeApp({
        credential: cert(serviceAccount),
        databaseURL: `https://${process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID}.firebaseio.com`,
      })

      console.log('✅ Firebase Admin SDK initialized successfully')
    } catch (error) {
      console.error("❌ Firebase admin initialization error:", error)
      process.exit(1)
    }
  }

  return getFirestore()
}

async function fixRemainingUser() {
  const userId = "2c2fxxCbwGcrS1tmaXGH9YPCMJH3"
  
  console.log(`🔧 Fixing remaining user: ${userId}`)
  
  const result = await AdminMigrationService.migrateUserSubscription(userId)
  
  if (result.success) {
    console.log(`✅ User fixed successfully`)
    console.log(`  📝 Entries created: ${result.data!.entriesCreated}`)
    console.log(`  📋 Reason: ${result.data!.reason}`)
  } else {
    console.error(`❌ Failed to fix user:`, result.error)
  }
}

async function main() {
  try {
    await initializeFirebase()
    await fixRemainingUser()
    
    console.log('\n🔍 Running validation...')
    const validationResult = await AdminMigrationService.validateMigration()
    
    if (validationResult.success) {
      const { isValid, issues, statistics } = validationResult.data!
      
      console.log("📊 Final Statistics:")
      console.log(`  Total users: ${statistics.totalUsers}`)
      console.log(`  Users with subscriptions: ${statistics.usersWithSubscriptions}`)
      console.log(`  Total subscription entries: ${statistics.totalEntries}`)
      console.log(`  Free entries: ${statistics.freeEntries}`)
      console.log(`  Stripe entries: ${statistics.stripeEntries}`)
      console.log(`  Old structure entries: ${statistics.oldStructureEntries}`)
      console.log(`  Users without subscription entries: ${statistics.usersWithoutFreeEntry}`)
      
      if (isValid) {
        console.log("\n✅ Migration validation PASSED")
        console.log("🎉 All users successfully migrated to flat subscription structure!")
      } else {
        console.log("\n❌ Migration validation FAILED")
        console.log("\n🚨 Remaining issues:")
        issues.forEach((issue: string, index: number) => {
          console.log(`  ${index + 1}. ${issue}`)
        })
      }
    }
    
  } catch (error) {
    console.error('❌ Fix failed:', error)
    process.exit(1)
  }
}

main().catch((error) => {
  console.error('💥 Script failed:', error)
  process.exit(1)
})
