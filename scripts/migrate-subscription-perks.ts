#!/usr/bin/env tsx

/**
 * Migration Script for Subscription Perks to Flat Subscription System
 *
 * This script migrates unlocked subscription perks to the flat subscription system.
 * It applies unlocked perks and creates perk subscription entries.
 *
 * Usage:
 *   npm run migrate:subscription-perks [options]
 *
 * Options:
 *   --dry-run          Show what would be migrated without making changes
 *   --validate-only    Only validate current state, don't migrate
 *   --user-id <id>     Migrate only a specific user
 *
 * Examples:
 *   tsx scripts/migrate-subscription-perks.ts --dry-run
 *   tsx scripts/migrate-subscription-perks.ts --validate-only
 *   tsx scripts/migrate-subscription-perks.ts
 */

import { config } from "dotenv"
import { resolve } from "path"
import { initializeApp, cert, getApps } from "firebase-admin/app"
import { getFirestore } from "firebase-admin/firestore"

// Parse command line arguments
const args = process.argv.slice(2)
const isDryRun = args.includes("--dry-run")
const isValidateOnly = args.includes("--validate-only")
const specificUserId = args.find((arg) => arg.startsWith("--user-id="))?.split("=")[1]
const envFile = args.find((arg) => !arg.startsWith("--")) || ".env.local"

// Initialize Firebase Admin SDK
async function initializeFirebase() {
  const envPath = resolve(process.cwd(), envFile)
  console.log(`Loading environment variables from: ${envPath}`)
  config({ path: envPath })

  if (!getApps().length) {
    try {
      if (!process.env.FIREBASE_SERVICE_ACCOUNT_KEY) {
        throw new Error("FIREBASE_SERVICE_ACCOUNT_KEY environment variable is not set")
      }

      const serviceAccount = JSON.parse(process.env.FIREBASE_SERVICE_ACCOUNT_KEY)

      initializeApp({
        credential: cert(serviceAccount),
        databaseURL: `https://${process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID}.firebaseio.com`,
      })

      console.log("✅ Firebase Admin SDK initialized successfully")
    } catch (error) {
      console.error("❌ Firebase admin initialization error:", error)
      process.exit(1)
    }
  }

  return getFirestore()
}

async function validateCurrentState() {
  console.log("🔍 Validating current subscription perks state...\n")

  const db = getFirestore()

  // Get all users
  const usersSnapshot = await db.collection("users").get()

  let totalUsers = usersSnapshot.size
  let usersWithSubscriptionPerks = 0
  let unlockedSubscriptionPerks = 0
  let appliedSubscriptionPerks = 0
  let perkEntriesInFlat = 0

  const usersNeedingMigration: any[] = []

  // Check user perks
  for (const userDoc of usersSnapshot.docs) {
    const userId = userDoc.id
    const perksSnapshot = await db.collection("users").doc(userId).collection("perks").get()

    let hasSubscriptionPerks = false

    for (const perkDoc of perksSnapshot.docs) {
      const perkData = perkDoc.data()

      if (perkData.perkDetails?.perkType === "subscription") {
        hasSubscriptionPerks = true

        if (perkData.status === "unlocked") {
          unlockedSubscriptionPerks++
          usersNeedingMigration.push({
            userId,
            perkId: perkDoc.id,
            perkName: perkData.perkDetails?.name,
            duration: perkData.perkDetails?.perkValue?.duration || 30,
          })
        } else if (perkData.status === "applied") {
          appliedSubscriptionPerks++
        }
      }
    }

    if (hasSubscriptionPerks) {
      usersWithSubscriptionPerks++
    }
  }

  // Check existing perk entries in flat subscription
  const subscriptionsSnapshot = await db.collection("userSubscriptions").get()
  for (const subDoc of subscriptionsSnapshot.docs) {
    const subData = subDoc.data()
    if (subData.source === "perk") {
      perkEntriesInFlat++
    }
  }

  console.log("📊 Current State:")
  console.log(`  Total users: ${totalUsers}`)
  console.log(`  Users with subscription perks: ${usersWithSubscriptionPerks}`)
  console.log(`  Unlocked subscription perks: ${unlockedSubscriptionPerks}`)
  console.log(`  Applied subscription perks: ${appliedSubscriptionPerks}`)
  console.log(`  Perk entries in flat subscription: ${perkEntriesInFlat}`)

  if (usersNeedingMigration.length > 0) {
    console.log("\n🔄 Users needing migration:")
    usersNeedingMigration.forEach((user, index) => {
      console.log(`  ${index + 1}. User: ${user.userId}`)
      console.log(`     Perk: ${user.perkName} (${user.duration} days)`)
    })
  } else {
    console.log("\n✅ No subscription perks need migration")
  }

  return {
    usersNeedingMigration,
    stats: {
      totalUsers,
      usersWithSubscriptionPerks,
      unlockedSubscriptionPerks,
      appliedSubscriptionPerks,
      perkEntriesInFlat,
    },
  }
}

async function migrateUserSubscriptionPerk(userId: string, perkId: string, perkData: any) {
  const db = getFirestore()

  console.log(`🔄 Migrating perk ${perkId} for user ${userId}...`)

  // Calculate dates outside transaction for logging
  const duration = perkData.duration || 30
  const startDate = new Date()
  const endDate = new Date(startDate.getTime() + duration * 24 * 60 * 60 * 1000)

  try {
    await db.runTransaction(async (transaction) => {
      // 1. Get the user perk document
      const userPerkRef = db.collection("users").doc(userId).collection("perks").doc(perkId)
      const userPerkDoc = await transaction.get(userPerkRef)

      if (!userPerkDoc.exists) {
        throw new Error(`User perk ${perkId} not found`)
      }

      const userPerk = userPerkDoc.data()

      if (userPerk?.status !== "unlocked") {
        throw new Error(`Perk ${perkId} is not in unlocked status (current: ${userPerk?.status})`)
      }

      // 2. Create perk subscription entry in flat system
      const perkSubscriptionEntry = {
        userId,
        source: "perk",
        status: "applied",
        precedence: 1, // Highest precedence
        startDate: startDate,
        endDate: endDate,
        subscriptionData: {
          perkId,
          appliedAt: startDate,
          duration,
        },
      }

      const newSubscriptionRef = db.collection("userSubscriptions").doc()
      transaction.set(newSubscriptionRef, perkSubscriptionEntry)

      // 3. Update perk status to applied
      transaction.update(userPerkRef, {
        status: "applied",
        appliedAt: startDate,
        expiresAt: endDate,
        updatedAt: new Date(),
      })

      // 4. Update any existing applied entries to pending (perk has highest precedence)
      // We'll handle this in a separate query since we can't query in a transaction
    })

    // 5. Update other subscription entries to pending status
    const userSubscriptionsQuery = await db
      .collection("userSubscriptions")
      .where("userId", "==", userId)
      .where("status", "==", "applied")
      .get()

    const batch = db.batch()
    let updatedEntries = 0

    for (const doc of userSubscriptionsQuery.docs) {
      const data = doc.data()
      // Don't update the perk entry we just created
      if (data.source !== "perk" || data.subscriptionData?.perkId !== perkId) {
        batch.update(doc.ref, { status: "pending" })
        updatedEntries++
      }
    }

    if (updatedEntries > 0) {
      await batch.commit()
      console.log(`  📝 Updated ${updatedEntries} other subscription entries to pending status`)
    }

    console.log(`  ✅ Successfully migrated perk ${perkId} for user ${userId}`)
    console.log(`  🎯 Created perk subscription entry (${duration} days)`)
    console.log(`  📅 Valid from ${startDate.toISOString()} to ${endDate.toISOString()}`)

    return { success: true }
  } catch (error) {
    console.error(`  ❌ Failed to migrate perk ${perkId} for user ${userId}:`, error)
    return { success: false, error }
  }
}

async function runDryRun() {
  console.log("🔍 [DRY RUN] Subscription perks migration preview\n")

  const { usersNeedingMigration } = await validateCurrentState()

  if (usersNeedingMigration.length === 0) {
    console.log("\n✅ No subscription perks need migration")
    return
  }

  console.log("\n📋 Would perform the following actions:")

  usersNeedingMigration.forEach((user, index) => {
    console.log(`\n${index + 1}. User: ${user.userId}`)
    console.log(`   🎯 Apply perk: ${user.perkName}`)
    console.log(`   📅 Duration: ${user.duration} days`)
    console.log(`   📝 Actions:`)
    console.log(`      - Create perk subscription entry in userSubscriptions collection`)
    console.log(`      - Update perk status from 'unlocked' to 'applied'`)
    console.log(
      `      - Set other user's subscription entries to 'pending' (perk has highest precedence)`
    )
  })

  console.log("\n💡 To perform the actual migration, run without --dry-run flag")
}

async function runFullMigration() {
  console.log("🚀 Starting subscription perks migration...\n")

  const { usersNeedingMigration } = await validateCurrentState()

  if (usersNeedingMigration.length === 0) {
    console.log("\n✅ No subscription perks need migration")
    return
  }

  console.log(`\n🔄 Migrating ${usersNeedingMigration.length} subscription perks...`)

  let successCount = 0
  let errorCount = 0
  const errors: string[] = []

  for (const user of usersNeedingMigration) {
    if (specificUserId && user.userId !== specificUserId) {
      continue // Skip if specific user ID is provided and doesn't match
    }

    const result = await migrateUserSubscriptionPerk(user.userId, user.perkId, user)

    if (result.success) {
      successCount++
    } else {
      errorCount++
      errors.push(`User ${user.userId}, Perk ${user.perkId}: ${result.error}`)
    }
  }

  console.log(`\n📊 Migration Summary:`)
  console.log(`  ✅ Successful: ${successCount}`)
  console.log(`  ❌ Errors: ${errorCount}`)

  if (errors.length > 0) {
    console.log(`\n🚨 Errors:`)
    errors.forEach((error, index) => {
      console.log(`  ${index + 1}. ${error}`)
    })
  }

  // Final validation
  console.log("\n🔍 Running final validation...")
  await validateCurrentState()

  if (errorCount === 0) {
    console.log("\n🎉 Subscription perks migration completed successfully!")
  }
}

async function main() {
  try {
    await initializeFirebase()

    console.log("🚀 Subscription Perks Migration Tool")
    console.log("====================================")

    if (isValidateOnly) {
      await validateCurrentState()
      return
    }

    if (isDryRun) {
      await runDryRun()
      return
    }

    await runFullMigration()
  } catch (error) {
    console.error("❌ Migration failed:", error)
    process.exit(1)
  }
}

main().catch((error) => {
  console.error("💥 Script failed:", error)
  process.exit(1)
})
