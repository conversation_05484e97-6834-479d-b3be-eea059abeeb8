/**
 * <PERSON><PERSON><PERSON> to initialize global perk definitions in Firestore
 *
 * This script:
 * 1. Creates the default global perk definitions
 * 2. Can be safely re-run without duplicating perks
 *
 * Usage:
 * npx tsx scripts/initialize-global-perks.ts
 * npx tsx scripts/initialize-global-perks.ts .env.production
 * ENV_FILE=.env.staging npx tsx scripts/initialize-global-perks.ts
 */

import { initializeApp, getApps, cert } from "firebase-admin/app"
import { getFirestore } from "firebase-admin/firestore"
import { config } from "dotenv"
import { resolve } from "path"

// Load environment variables from specified .env file or default
const envFile = process.env.ENV_FILE || process.argv[2] || ".env.local"
const envPath = resolve(process.cwd(), envFile)

console.log(`Loading environment variables from: ${envPath}`)
config({ path: envPath })

// Initialize Firebase Admin SDK
if (!getApps().length) {
  try {
    if (!process.env.FIREBASE_SERVICE_ACCOUNT_KEY) {
      throw new Error("FIREBASE_SERVICE_ACCOUNT_KEY environment variable is not set")
    }

    const serviceAccount = JSON.parse(process.env.FIREBASE_SERVICE_ACCOUNT_KEY)

    initializeApp({
      credential: cert(serviceAccount),
      databaseURL: `https://${process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID}.firebaseio.com`,
    })
  } catch (error) {
    console.error("Firebase admin initialization error:", error)
    process.exit(1)
  }
}

const db = getFirestore()

/**
 * Default global perk definitions
 */
const DEFAULT_GLOBAL_PERKS = [
  {
    id: "squad_perk_5_referrals",
    name: "1 Free Squad",
    description: "Unlock 1 additional squad slot permanently",
    referralCount: 5,
    perkType: "squad",
    perkValue: {
      maxSquads: 1,
    },
    tags: ["squad", "permanent"],
    isActive: true,
  },
  {
    id: "subscription_perk_10_referrals",
    name: "2 Months Free Pro",
    description: "Get 2 months of Pro subscription for free",
    referralCount: 10,
    perkType: "subscription",
    perkValue: {
      duration: 60,
      unit: "days",
    },
    tags: ["subscription"],
    isActive: true,
  },
]

/**
 * Create or update a global perk
 */
async function createOrUpdateGlobalPerk(perkData: any): Promise<void> {
  const { id, ...data } = perkData

  const perkRef = db.collection("perks").doc(id)
  const existingPerk = await perkRef.get()

  if (existingPerk.exists) {
    console.log(`Perk ${id} already exists, updating...`)
    await perkRef.update({
      ...data,
      updatedAt: new Date(),
    })
  } else {
    console.log(`Creating new perk ${id}...`)
    await perkRef.set({
      ...data,
      createdAt: new Date(),
      updatedAt: new Date(),
    })
  }
}

/**
 * Main initialization function
 */
async function initializeGlobalPerks() {
  console.log("Initializing global perk definitions...")

  try {
    for (const perk of DEFAULT_GLOBAL_PERKS) {
      await createOrUpdateGlobalPerk(perk)
    }

    console.log(`Successfully initialized ${DEFAULT_GLOBAL_PERKS.length} global perks`)
  } catch (error) {
    console.error("Failed to initialize global perks:", error)
    process.exit(1)
  }
}

/**
 * Validate initialization results
 */
async function validateInitialization() {
  console.log("\nValidating global perk initialization...")

  try {
    const perksSnapshot = await db.collection("perks").get()

    console.log(`Found ${perksSnapshot.size} global perks`)

    perksSnapshot.forEach((doc) => {
      const data = doc.data()
      console.log(`- ${doc.id}: ${data.name} (${data.referralCount} referrals)`)
    })

    if (perksSnapshot.size >= DEFAULT_GLOBAL_PERKS.length) {
      console.log("✅ Global perk initialization successful")
    } else {
      console.log("⚠️  Some global perks may be missing")
    }
  } catch (error) {
    console.error("Validation failed:", error)
  }
}

// Run the initialization
if (import.meta.url === `file://${process.argv[1]}`) {
  initializeGlobalPerks()
    .then(() => validateInitialization())
    .then(() => {
      console.log("Global perk initialization completed")
      process.exit(0)
    })
    .catch((error) => {
      console.error("Global perk initialization failed:", error)
      process.exit(1)
    })
}

export { initializeGlobalPerks, validateInitialization }
