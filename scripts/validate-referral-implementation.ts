/**
 * Validation script to check referral system implementation
 *
 * This script validates:
 * 1. All required files exist
 * 2. Domain exports are properly configured
 * 3. Database structure is correct
 * 4. Integration points are working
 *
 * Usage:
 * npx tsx scripts/validate-referral-implementation.ts
 */

import { existsSync, readFileSync } from "fs"
import { join } from "path"

/**
 * Check if required files exist
 */
function validateFileStructure(): boolean {
  console.log("🔍 Validating file structure...")

  const requiredFiles = [
    // Referral domain
    "lib/domains/referral/referral.types.ts",
    "lib/domains/referral/referral.service.ts",
    "lib/domains/referral/referral.store.ts",
    "lib/domains/referral/referral.hooks.ts",
    "lib/domains/referral/referral.realtime.service.ts",
    "lib/domains/referral/referral.realtime.hooks.ts",
    "lib/domains/referral/index.ts",

    // Perk domain
    "lib/domains/perk/perk.types.ts",
    "lib/domains/perk/perk.service.ts",
    "lib/domains/perk/perk.store.ts",
    "lib/domains/perk/perk.hooks.ts",
    "lib/domains/perk/perk.realtime.service.ts",
    "lib/domains/perk/perk.realtime.hooks.ts",
    "lib/domains/perk/index.ts",

    // Enhanced subscription service
    "lib/domains/user-subscription/perk-aware-subscription.service.ts",

    // UI components
    "app/(authenticated)/settings/components/referrals-settings.tsx",
    "app/r/[code]/page.tsx",

    // Scripts
    "scripts/migrate-referral-codes.ts",
    "scripts/initialize-global-perks.ts",
    "scripts/setup-referral-system.ts",

    // Documentation
    "docs/REFERRAL_SYSTEM_TESTING.md",
    "scripts/README.md",
  ]

  let allFilesExist = true

  for (const file of requiredFiles) {
    const filePath = join(process.cwd(), file)
    if (existsSync(filePath)) {
      console.log(`✅ ${file}`)
    } else {
      console.log(`❌ ${file} - MISSING`)
      allFilesExist = false
    }
  }

  return allFilesExist
}

/**
 * Check domain exports
 */
function validateDomainExports(): boolean {
  console.log("\n🔍 Validating domain exports...")

  try {
    // Check if domains can be imported
    const domainsIndexPath = join(process.cwd(), "lib/domains/index.ts")
    if (!existsSync(domainsIndexPath)) {
      console.log("❌ lib/domains/index.ts not found")
      return false
    }

    // Read the file and check for referral and perk exports
    const content = readFileSync(domainsIndexPath, "utf8")

    const requiredExports = [
      "referral/referral.types",
      "referral/referral.service",
      "referral/referral.store",
      "referral/referral.hooks",
      "perk/perk.types",
      "perk/perk.service",
      "perk/perk.store",
      "perk/perk.hooks",
      "perk-aware-subscription.service",
    ]

    let allExportsPresent = true

    for (const exportPath of requiredExports) {
      if (content.includes(exportPath)) {
        console.log(`✅ Export: ${exportPath}`)
      } else {
        console.log(`❌ Export missing: ${exportPath}`)
        allExportsPresent = false
      }
    }

    return allExportsPresent
  } catch (error) {
    console.log("❌ Error validating domain exports:", error)
    return false
  }
}

/**
 * Check signup form integration
 */
function validateSignupIntegration(): boolean {
  console.log("\n🔍 Validating signup form integration...")

  try {
    const signupFormPath = join(process.cwd(), "app/signup/components/signup-form.tsx")
    if (!existsSync(signupFormPath)) {
      console.log("❌ Signup form not found")
      return false
    }

    const content = readFileSync(signupFormPath, "utf8")

    const requiredElements = ["referralCode", "setReferralCode", "referral_code", "Referral Code"]

    let allElementsPresent = true

    for (const element of requiredElements) {
      if (content.includes(element)) {
        console.log(`✅ Signup integration: ${element}`)
      } else {
        console.log(`❌ Signup integration missing: ${element}`)
        allElementsPresent = false
      }
    }

    return allElementsPresent
  } catch (error) {
    console.log("❌ Error validating signup integration:", error)
    return false
  }
}

/**
 * Check settings integration
 */
function validateSettingsIntegration(): boolean {
  console.log("\n🔍 Validating settings integration...")

  try {
    const settingsContentPath = join(
      process.cwd(),
      "app/(authenticated)/settings/components/settings-content.tsx"
    )
    if (!existsSync(settingsContentPath)) {
      console.log("❌ Settings content not found")
      return false
    }

    const content = readFileSync(settingsContentPath, "utf8")

    const requiredElements = ["referrals", "ReferralsSettings", "Referrals"]

    let allElementsPresent = true

    for (const element of requiredElements) {
      if (content.includes(element)) {
        console.log(`✅ Settings integration: ${element}`)
      } else {
        console.log(`❌ Settings integration missing: ${element}`)
        allElementsPresent = false
      }
    }

    return allElementsPresent
  } catch (error) {
    console.log("❌ Error validating settings integration:", error)
    return false
  }
}

/**
 * Check package.json scripts
 */
function validatePackageScripts(): boolean {
  console.log("\n🔍 Validating package.json scripts...")

  try {
    const packageJsonPath = join(process.cwd(), "package.json")
    if (!existsSync(packageJsonPath)) {
      console.log("❌ package.json not found")
      return false
    }

    const packageJson = JSON.parse(readFileSync(packageJsonPath, "utf8"))

    const requiredScripts = ["setup:referrals", "migrate:referral-codes", "init:global-perks"]

    let allScriptsPresent = true

    for (const script of requiredScripts) {
      if (packageJson.scripts && packageJson.scripts[script]) {
        console.log(`✅ Script: ${script}`)
      } else {
        console.log(`❌ Script missing: ${script}`)
        allScriptsPresent = false
      }
    }

    return allScriptsPresent
  } catch (error) {
    console.log("❌ Error validating package scripts:", error)
    return false
  }
}

/**
 * Main validation function
 */
async function validateImplementation() {
  console.log("🚀 Validating Referral System Implementation")
  console.log("=".repeat(50))

  const validations = [
    { name: "File Structure", fn: validateFileStructure },
    { name: "Domain Exports", fn: validateDomainExports },
    { name: "Signup Integration", fn: validateSignupIntegration },
    { name: "Settings Integration", fn: validateSettingsIntegration },
    { name: "Package Scripts", fn: validatePackageScripts },
  ]

  let allValidationsPassed = true

  for (const validation of validations) {
    const passed = validation.fn()
    if (!passed) {
      allValidationsPassed = false
    }
  }

  console.log("\n" + "=".repeat(50))

  if (allValidationsPassed) {
    console.log("✅ All validations passed!")
    console.log("\nNext steps:")
    console.log("1. Run migration scripts: npm run setup:referrals")
    console.log("2. Test the implementation using the testing guide")
    console.log("3. Deploy to staging environment")
    console.log("4. Conduct user acceptance testing")
  } else {
    console.log("❌ Some validations failed!")
    console.log("\nPlease fix the issues above before proceeding.")
  }

  return allValidationsPassed
}

// Run validation if this script is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  validateImplementation()
    .then((success) => {
      process.exit(success ? 0 : 1)
    })
    .catch((error) => {
      console.error("Validation failed:", error)
      process.exit(1)
    })
}

export { validateImplementation }
