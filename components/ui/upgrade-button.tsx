"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { cn } from "@/lib/utils"
import { useRouter } from "next/navigation"
import { Crown } from "lucide-react"

interface UpgradeButtonProps {
  className?: string
  showText?: boolean
}

export function UpgradeButton({ className, showText = true }: UpgradeButtonProps) {
  const router = useRouter()

  const handleUpgrade = () => {
    router.push("/settings?tab=billing")
  }

  return (
    <Button
      onClick={handleUpgrade}
      className={cn(
        // Use the same gradient as message input upgrade button
        "bg-gradient-to-r from-yellow-400 to-orange-500 hover:from-yellow-500 hover:to-orange-600",
        "text-white font-medium",
        // Touch-friendly sizing for mobile
        "min-h-[44px] min-w-[44px]",
        // Smooth transitions
        "transition-all duration-300 ease-in-out",
        "hover:scale-105 active:scale-95",
        // Focus styles
        "focus:outline-none focus:ring-0 focus:ring-offset-0",
        className
      )}
      size={showText ? "sm" : "icon"}
      aria-label="Upgrade to Pro"
    >
      <Crown className={cn("h-4 w-4", showText && "mr-2")} />
      {showText && <span>Upgrade to Pro</span>}
    </Button>
  )
}
