"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { AlertCircle, X } from "lucide-react"
import Link from "next/link"
import {
  SubscriptionErrorType,
  getSubscriptionErrorAlert,
} from "@/lib/domains/user-subscription/user-subscription.errors"
import { useIsUserSubscribed } from "@/lib/domains/user-subscription/user-subscription.hooks"

interface AIUsageWarningProps {
  errorType: SubscriptionErrorType
  usageData: {
    daily: number
    weekly: number
    dailyLimit: number
    weeklyLimit: number
    categoryCount?: number
    categoryLimit?: number
  }
  className?: string
  onClose?: () => void
}

export function AIUsageWarning({
  errorType,
  usageData,
  className = "",
  onClose,
}: AIUsageWarningProps) {
  const [dismissed, setDismissed] = useState(false)
  const isSubscribed = useIsUserSubscribed()

  if (dismissed) {
    return null
  }

  const errorAlert = getSubscriptionErrorAlert(errorType, isSubscribed)
  // const isDailyLimit = errorType === SubscriptionErrorType.DAILY_AI_LIMIT_REACHED

  const handleDismiss = () => {
    setDismissed(true)
    if (onClose) {
      onClose()
    }
  }

  return (
    <div
      className={`mb-4 mt-4 rounded-lg border border-destructive/50 bg-background p-4 text-destructive dark:border-destructive ${className}`}
    >
      {/* Top portion: alert icon, header, and close button */}
      <div className="flex items-center justify-between mb-2">
        <div className="flex items-center gap-2">
          <AlertCircle className="h-4 w-4 text-destructive" />
          <h5 className="font-medium">{errorAlert.title}</h5>
        </div>
        <Button
          variant="ghost"
          size="icon"
          className="h-6 w-6 p-0 flex items-center justify-center"
          onClick={handleDismiss}
          aria-label="Close"
        >
          <X className="h-4 w-4" />
        </Button>
      </div>

      {/* Middle portion: content */}
      <div className="space-y-2 mb-3">
        <p className="text-sm">{errorAlert.description}</p>

        <div className="text-sm">
          {(errorType === SubscriptionErrorType.TRIP_AI_LIMIT_REACHED ||
            errorType === SubscriptionErrorType.TASK_AI_LIMIT_REACHED ||
            errorType === SubscriptionErrorType.ITINERARY_AI_LIMIT_REACHED) &&
          usageData.categoryCount !== undefined &&
          usageData.categoryLimit !== undefined ? (
            <div className="flex justify-between items-center">
              {errorType === SubscriptionErrorType.TRIP_AI_LIMIT_REACHED ? (
                <span>Trip usage:</span>
              ) : errorType === SubscriptionErrorType.TASK_AI_LIMIT_REACHED ? (
                <span>Task usage:</span>
              ) : (
                <span>Itinerary usage:</span>
              )}
              <span className="font-medium">
                {usageData.categoryCount} / {usageData.categoryLimit}
              </span>
            </div>
          ) : (
            <>
              <div className="flex justify-between items-center">
                <span>Category usage:</span>
                <span className="font-medium">
                  {usageData.categoryCount !== undefined && usageData.categoryLimit !== undefined
                    ? `${usageData.categoryCount} / ${usageData.categoryLimit}`
                    : "Limit reached"}
                </span>
              </div>
            </>
          )}
        </div>
      </div>

      {/* Bottom portion: upgrade button */}
      <div>
        <Button variant="outline" size="sm" asChild>
          <Link href="/settings?tab=billing">Upgrade to Pro</Link>
        </Button>
        {/* {isDailyLimit && (
          <Button variant="ghost" size="sm" onClick={handleDismiss} className="ml-2">
            Remind me tomorrow
          </Button>
        )} */}
      </div>
    </div>
  )
}
