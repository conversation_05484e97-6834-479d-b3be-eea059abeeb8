# Multi-Entry Subscription System Implementation

## Overview

Redesign the user-subscription system to support multiple subscription sources (Stripe, perks, giveaways) with proper precedence, status tracking, and Stripe subscription pausing when perk-based subscriptions are active.

## ARCHITECTURE DECISION UPDATE

After review, we'll enhance the existing UserSubscription structure instead of creating a separate UserSubscriptionEntry system:

- Use `userSubscriptions/{userId}/entries/{entryId}` subcollection within existing structure
- Maintain backward compatibility with existing UserSubscription interface
- Enhance existing services rather than creating completely new ones
- Reuse existing collection structure and patterns

## Current State Analysis

- Single UserSubscription document per user at `userSubscriptions/{userId}`
- Direct Stripe integration with single subscription tracking
- Perk system exists but subscription perks are applied by extending current subscription
- No precedence system for multiple subscription sources
- No status tracking for different subscription types

## Target Architecture (REVISED)

- Enhanced UserSubscription with entries subcollection: `userSubscriptions/{userId}/entries/{entryId}`
- Precedence-based system: Perk > Giveaway > Stripe
- Status tracking: `applied`, `pending`, `paused`, `expired`
- Stripe subscription pausing when higher precedence subscriptions are active
- Cron job for expiration management
- Maintain existing UserSubscription interface for backward compatibility

## Tasks

### Phase 1: Schema Design & Types ✅ COMPLETED

- [x] Design new UserSubscriptionEntry interface with source tracking
- [x] Add status field for entry state management
- [x] Define subscription source types and precedence order
- [x] Create migration strategy from single to multi-entry system
- [x] Update TypeScript types and interfaces

### Phase 2: Core Service Layer ✅ COMPLETED

- [x] Create UserSubscriptionEntryService for CRUD operations
- [x] Implement precedence calculation logic
- [x] Add entry status management (apply/pause/expire)
- [x] Create subscription aggregation service
- [x] Update existing UserSubscriptionService to use new system

### Phase 3: Stripe Integration Updates ✅ COMPLETED

- [x] Add Stripe subscription pausing functionality
- [x] Update webhook handlers for multi-entry system
- [x] Implement Stripe subscription resumption logic
- [x] Handle Stripe cancellation with entry removal
- [x] Ensure single Stripe entry per user constraint

### Phase 4: Perk System Integration ✅ COMPLETED

- [x] Update perk application to create subscription entries
- [x] Implement perk-based subscription precedence
- [x] Add perk expiration handling
- [x] Update PerkAwareSubscriptionService for new system

### Phase 5: Data Migration ✅ COMPLETED

- [x] Create migration script for existing subscriptions
- [x] Backup existing subscription data
- [x] Convert single subscriptions to entry format
- [x] Validate migration results
- [x] Update production data safely

### Phase 6: Cron Job Implementation ✅ COMPLETED

- [x] Create subscription entry expiration cron job
- [x] Implement precedence recalculation on expiry
- [x] Add Stripe subscription resumption logic
- [x] Handle edge cases and error scenarios
- [x] Add monitoring and logging

### Phase 7: Store & Hook Updates 🔄 IN PROGRESS

- [x] Update UserSubscriptionStore for multi-entry system
- [x] Modify realtime hooks for entry subscriptions
- [x] Update computed properties for aggregated state
- [ ] Ensure backward compatibility during transition
- [ ] Update existing hooks to use new system
- [ ] Test store integration with components

### Phase 8: Testing & Validation ⏳ PENDING

- [ ] Create comprehensive test suite
- [ ] Test precedence scenarios
- [ ] Validate Stripe pausing/resumption
- [ ] Test perk application and expiration
- [ ] End-to-end integration testing

## NEXT STEPS & ARCHITECTURE REVISION

### Immediate Actions Required:

1. **Simplify Architecture**: Instead of UserSubscriptionEntry, enhance existing UserSubscription
2. **Use Existing Collection**: Keep `userSubscriptions/{userId}` but add `entries` subcollection
3. **Maintain Compatibility**: Ensure existing code continues to work during transition
4. **Refactor Services**: Update existing services instead of creating completely new ones

### Recommended Approach:

1. ✅ Enhance UserSubscription interface to support multiple sources
2. ✅ Add `userSubscriptions/{userId}/entries/{entryId}` subcollection
3. ✅ Create EnhancedSubscriptionService that works with existing collection
4. ✅ Create SimpleMigrationService for safe, non-destructive migration
5. ⏳ Update existing UserSubscriptionService methods to use enhanced system
6. ⏳ Gradually migrate components to use enhanced system

### Files Created (Revised Architecture):

#### Core Services:

- ✅ `enhanced-subscription.service.ts` - Works with existing collection + entries subcollection
- ✅ `simple-migration.service.ts` - Safe migration without breaking changes
- ✅ Enhanced `user-subscription.types.ts` - Backward compatible interface

#### Migration Strategy:

1. **Phase 1**: Add entries subcollection alongside existing subscription documents
2. **Phase 2**: Migrate existing Stripe subscriptions to entries format
3. **Phase 3**: Update services to read from both main doc and entries
4. **Phase 4**: Gradually update components to use enhanced system
5. **Phase 5**: Eventually deprecate direct main document updates

### Benefits of Revised Approach:

- ✅ **Zero Breaking Changes**: Existing code continues to work
- ✅ **Gradual Migration**: Can migrate users one by one
- ✅ **Backward Compatibility**: Main subscription document still works
- ✅ **Enhanced Features**: Multi-source support via subcollection
- ✅ **Safe Rollback**: Can revert by ignoring subcollection

## Schema Design Details

### UserSubscriptionEntry Interface

```typescript
interface UserSubscriptionEntry extends BaseEntity {
  userId: string
  source: "stripe" | "perk" | "giveaway"
  status: "applied" | "pending" | "paused" | "expired"
  precedence: number // 1=highest (perk), 2=medium (giveaway), 3=lowest (stripe)

  // Subscription details
  subscriptionPlan: SubscriptionPlan
  startDate: Timestamp
  endDate: Timestamp | null // null for indefinite

  // Source-specific data
  stripeData?: {
    customerId: string
    subscriptionId: string
    subscriptionStatus: SubscriptionStatus
    currentPeriodEnd: Timestamp
  }

  perkData?: {
    perkId: string
    appliedAt: Timestamp
    duration: number // in days
  }

  giveawayData?: {
    giveawayId: string
    duration: number // in days
  }
}
```

### Precedence Rules

1. **Perk subscriptions** (precedence: 1) - Highest priority
2. **Giveaway subscriptions** (precedence: 2) - Medium priority
3. **Stripe subscriptions** (precedence: 3) - Lowest priority

### Status Management

- **applied**: Currently active and providing benefits
- **pending**: Waiting to be applied (lower precedence active)
- **paused**: Temporarily inactive (higher precedence active)
- **expired**: No longer valid

## Implementation Notes

- Only one entry per source type per user (especially Stripe)
- Stripe subscriptions are paused (not cancelled) when higher precedence entries are active
- Cron job runs daily to check for expirations and update precedence
- Migration must be atomic and reversible
- Maintain backward compatibility during transition period

## Success Criteria

- [ ] Multiple subscription sources work simultaneously
- [ ] Precedence system correctly prioritizes subscriptions
- [ ] Stripe subscriptions pause/resume automatically
- [ ] Perk-based subscriptions integrate seamlessly
- [ ] Existing functionality remains unaffected
- [ ] Performance impact is minimal
- [ ] Data integrity is maintained throughout migration
